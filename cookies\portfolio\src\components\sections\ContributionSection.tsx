"use client";

import { useState } from "react";
import { ExternalLink } from "lucide-react";

export default function ContributionSection() {
  const username = 'Arkit-k';
  const [selectedYear, setSelectedYear] = useState('2025');
  const years = ['2025', '2024', '2023', '2022', '2021'];

  return (
    <section className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="bg-gray-900 rounded-2xl p-8 border border-gray-700">

          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold text-white mb-2">
                Contribution Graph
              </h2>
              <div className="flex items-center gap-4 text-sm text-gray-400">
                <span>167 contributions in the last year</span>
                <div className="flex items-center gap-2">
                  <span>Less</span>
                  <div className="flex gap-1">
                    <div className="w-3 h-3 bg-gray-700 rounded-sm"></div>
                    <div className="w-3 h-3 bg-green-900 rounded-sm"></div>
                    <div className="w-3 h-3 bg-green-700 rounded-sm"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-sm"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-sm"></div>
                  </div>
                  <span>More</span>
                </div>
              </div>
            </div>

            {/* Year Selector */}
            <div className="flex flex-col gap-2">
              {years.map((year) => (
                <button
                  key={year}
                  onClick={() => setSelectedYear(year)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    selectedYear === year
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-800 text-gray-400 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  {year}
                </button>
              ))}
            </div>
          </div>

          {/* Contribution Grid */}
          <div className="bg-gray-800/50 rounded-xl p-6">
            {/* Month labels */}
            <div className="flex justify-between text-xs text-gray-400 mb-3 px-8">
              {['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'].map((month) => (
                <span key={month}>{month}</span>
              ))}
            </div>

            {/* Day labels and grid */}
            <div className="flex gap-2">
              {/* Day labels */}
              <div className="flex flex-col justify-between text-xs text-gray-400 py-1">
                <span>Mon</span>
                <span>Wed</span>
                <span>Fri</span>
              </div>

              {/* Contribution grid */}
              <div className="flex-1">
                <div className="grid grid-cols-53 gap-1">
                  {Array.from({ length: 371 }, (_, i) => {
                    // Generate random contribution levels for demo
                    const level = Math.floor(Math.random() * 5);
                    const colors = [
                      'bg-gray-700',
                      'bg-green-900',
                      'bg-green-700',
                      'bg-green-500',
                      'bg-green-400'
                    ];
                    return (
                      <div
                        key={i}
                        className={`w-3 h-3 rounded-sm ${colors[level]} hover:ring-1 hover:ring-white/50 transition-all cursor-pointer`}
                        title={`${level} contributions`}
                      />
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-6 flex items-center justify-between">
            <p className="text-sm text-gray-500">
              Learn how we count contributions
            </p>
            <a
              href={`https://github.com/${username}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 text-sm"
            >
              <ExternalLink className="w-4 h-4" />
              View on GitHub
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}