"use client";

import Image from "next/image";
import { ExternalLink } from "lucide-react";

export default function ContributionSection() {
  const username = 'Arkit-k';

  return (
    <section className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="bg-deep-charcoal dark:bg-dark-surface rounded-2xl p-8 lg:p-12">

          {/* Header Section */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl lg:text-4xl font-bold text-white mb-2">
                GitHub Contributions
              </h2>
              <p className="text-gray-400 text-lg">
                My coding activity and contributions over time
              </p>
            </div>
            <a
              href={`https://github.com/${username}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 px-6 py-3 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg transition-colors duration-200"
            >
              <ExternalLink className="w-5 h-5" />
              View Profile
            </a>
          </div>

          {/* Contribution Graph */}
          <div className="bg-gray-900/50 rounded-xl p-6 border border-gray-700">
            <Image
              src={`https://ghchart.rshah.org/${username}`}
              alt={`${username}'s GitHub Contribution Graph`}
              width={1000}
              height={200}
              className="w-full rounded-lg"
              unoptimized
            />
          </div>
        </div>
      </div>
    </section>
  );
}