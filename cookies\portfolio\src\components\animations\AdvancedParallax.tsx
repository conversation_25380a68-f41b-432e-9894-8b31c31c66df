"use client";

import { useEffect, useRef } from 'react';
import { motion, useScroll, useTransform, useSpring, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

// Smooth reveal animation with GSAP
export function SmoothReveal({ 
  children, 
  className = '',
  direction = 'up',
  delay = 0,
  duration = 1
}: { 
  children: React.ReactNode; 
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
  duration?: number;
}) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      const element = ref.current;
      
      const getInitialTransform = () => {
        switch (direction) {
          case 'up': return { y: 100, opacity: 0 };
          case 'down': return { y: -100, opacity: 0 };
          case 'left': return { x: 100, opacity: 0 };
          case 'right': return { x: -100, opacity: 0 };
          default: return { y: 100, opacity: 0 };
        }
      };

      gsap.fromTo(element, 
        getInitialTransform(),
        {
          x: 0,
          y: 0,
          opacity: 1,
          duration,
          delay,
          ease: "power3.out",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );
    }
  }, [direction, delay, duration]);

  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  );
}

// Staggered children animation
export function StaggerContainer({ 
  children, 
  className = '',
  stagger = 0.1
}: { 
  children: React.ReactNode; 
  className?: string;
  stagger?: number;
}) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      const children = ref.current.children;
      
      gsap.fromTo(children, 
        { y: 50, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.8,
          stagger,
          ease: "power3.out",
          scrollTrigger: {
            trigger: ref.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );
    }
  }, [stagger]);

  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  );
}

// Morphing background effect
export function MorphingBackground({ 
  className = '',
  colors = ['#10B981', '#3B82F6', '#8B5CF6']
}: { 
  className?: string;
  colors?: string[];
}) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      const tl = gsap.timeline({ repeat: -1 });
      
      colors.forEach((color, index) => {
        tl.to(ref.current, {
          backgroundColor: color,
          duration: 3,
          ease: "power2.inOut"
        });
      });
    }
  }, [colors]);

  return <div ref={ref} className={className} />;
}

// Text reveal with typewriter effect
export function TypewriterText({ 
  text, 
  className = '',
  speed = 0.05
}: { 
  text: string; 
  className?: string;
  speed?: number;
}) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      const element = ref.current;
      element.textContent = '';
      
      gsap.to({}, {
        duration: text.length * speed,
        ease: "none",
        onUpdate: function() {
          const progress = this.progress();
          const currentLength = Math.floor(progress * text.length);
          element.textContent = text.slice(0, currentLength);
        },
        scrollTrigger: {
          trigger: element,
          start: "top 80%",
          toggleActions: "play none none reverse"
        }
      });
    }
  }, [text, speed]);

  return <div ref={ref} className={className} />;
}

// Parallax layers with different speeds
export function ParallaxLayers({ 
  layers,
  className = ''
}: { 
  layers: Array<{
    content: React.ReactNode;
    speed: number;
    className?: string;
  }>;
  className?: string;
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {layers.map((layer, index) => {
        const y = useSpring(
          useTransform(scrollYProgress, [0, 1], [0, -layer.speed * 100]),
          { stiffness: 100, damping: 30 }
        );

        return (
          <motion.div
            key={index}
            style={{ y }}
            className={`absolute inset-0 ${layer.className || ''}`}
          >
            {layer.content}
          </motion.div>
        );
      })}
    </div>
  );
}

// 3D tilt effect on hover
export function TiltCard({ 
  children, 
  className = '',
  maxTilt = 15
}: { 
  children: React.ReactNode; 
  className?: string;
  maxTilt?: number;
}) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const rotateX = ((y - centerY) / centerY) * maxTilt;
      const rotateY = ((centerX - x) / centerX) * maxTilt;

      gsap.to(element, {
        rotateX,
        rotateY,
        duration: 0.3,
        ease: "power2.out",
        transformPerspective: 1000
      });
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        rotateX: 0,
        rotateY: 0,
        duration: 0.5,
        ease: "power2.out"
      });
    };

    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [maxTilt]);

  return (
    <div ref={ref} className={className} style={{ transformStyle: 'preserve-3d' }}>
      {children}
    </div>
  );
}

// Scroll-triggered counter animation
export function AnimatedCounter({ 
  end, 
  duration = 2,
  className = ''
}: { 
  end: number; 
  duration?: number;
  className?: string;
}) {
  const ref = useRef<HTMLSpanElement>(null);
  const isInView = useInView(ref, { once: true });

  useEffect(() => {
    if (isInView && ref.current) {
      gsap.to({ value: 0 }, {
        value: end,
        duration,
        ease: "power2.out",
        onUpdate: function() {
          if (ref.current) {
            ref.current.textContent = Math.floor(this.targets()[0].value).toString();
          }
        }
      });
    }
  }, [isInView, end, duration]);

  return <span ref={ref} className={className}>0</span>;
}
