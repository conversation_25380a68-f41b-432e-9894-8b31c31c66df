"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inView: () => (/* binding */ inView)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry.target, entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (typeof onEnd === \"function\") {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-in-view.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInView: () => (/* binding */ useInView)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../render/dom/viewport/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\");\n\n\n\nfunction useInView(ref, { root, margin, amount, once = false, initial = false, } = {}) {\n    const [isInView, setInView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return (0,_render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__.inView)(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWluLXZpZXcubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNjOztBQUUxRCwwQkFBMEIsdURBQXVELElBQUk7QUFDckYsa0NBQWtDLCtDQUFRO0FBQzFDLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzRUFBTTtBQUNyQixLQUFLO0FBQ0w7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYXJraXRcXERlc2t0b3BcXGNvb2tpZXNcXHBvcnRmb2xpb1xcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcdXRpbHNcXHVzZS1pbi12aWV3Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgaW5WaWV3IH0gZnJvbSAnLi4vcmVuZGVyL2RvbS92aWV3cG9ydC9pbmRleC5tanMnO1xuXG5mdW5jdGlvbiB1c2VJblZpZXcocmVmLCB7IHJvb3QsIG1hcmdpbiwgYW1vdW50LCBvbmNlID0gZmFsc2UsIGluaXRpYWwgPSBmYWxzZSwgfSA9IHt9KSB7XG4gICAgY29uc3QgW2lzSW5WaWV3LCBzZXRJblZpZXddID0gdXNlU3RhdGUoaW5pdGlhbCk7XG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKCFyZWYuY3VycmVudCB8fCAob25jZSAmJiBpc0luVmlldykpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIGNvbnN0IG9uRW50ZXIgPSAoKSA9PiB7XG4gICAgICAgICAgICBzZXRJblZpZXcodHJ1ZSk7XG4gICAgICAgICAgICByZXR1cm4gb25jZSA/IHVuZGVmaW5lZCA6ICgpID0+IHNldEluVmlldyhmYWxzZSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IG9wdGlvbnMgPSB7XG4gICAgICAgICAgICByb290OiAocm9vdCAmJiByb290LmN1cnJlbnQpIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIG1hcmdpbixcbiAgICAgICAgICAgIGFtb3VudCxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIGluVmlldyhyZWYuY3VycmVudCwgb25FbnRlciwgb3B0aW9ucyk7XG4gICAgfSwgW3Jvb3QsIHJlZiwgbWFyZ2luLCBvbmNlLCBhbW91bnRdKTtcbiAgICByZXR1cm4gaXNJblZpZXc7XG59XG5cbmV4cG9ydCB7IHVzZUluVmlldyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/animations/AdvancedParallax.tsx":
/*!********************************************************!*\
  !*** ./src/components/animations/AdvancedParallax.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedCounter: () => (/* binding */ AnimatedCounter),\n/* harmony export */   MorphingBackground: () => (/* binding */ MorphingBackground),\n/* harmony export */   ParallaxLayers: () => (/* binding */ ParallaxLayers),\n/* harmony export */   SmoothReveal: () => (/* binding */ SmoothReveal),\n/* harmony export */   StaggerContainer: () => (/* binding */ StaggerContainer),\n/* harmony export */   TiltCard: () => (/* binding */ TiltCard),\n/* harmony export */   TypewriterText: () => (/* binding */ TypewriterText)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ SmoothReveal,StaggerContainer,MorphingBackground,TypewriterText,ParallaxLayers,TiltCard,AnimatedCounter auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$();\n\n\n\n\n// Register GSAP plugins\nif (true) {\n    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\n}\n// Smooth reveal animation with GSAP\nfunction SmoothReveal(param) {\n    let { children, className = '', direction = 'up', delay = 0, duration = 1 } = param;\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmoothReveal.useEffect\": ()=>{\n            if (ref.current) {\n                const element = ref.current;\n                const getInitialTransform = {\n                    \"SmoothReveal.useEffect.getInitialTransform\": ()=>{\n                        switch(direction){\n                            case 'up':\n                                return {\n                                    y: 100,\n                                    opacity: 0\n                                };\n                            case 'down':\n                                return {\n                                    y: -100,\n                                    opacity: 0\n                                };\n                            case 'left':\n                                return {\n                                    x: 100,\n                                    opacity: 0\n                                };\n                            case 'right':\n                                return {\n                                    x: -100,\n                                    opacity: 0\n                                };\n                            default:\n                                return {\n                                    y: 100,\n                                    opacity: 0\n                                };\n                        }\n                    }\n                }[\"SmoothReveal.useEffect.getInitialTransform\"];\n                gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(element, getInitialTransform(), {\n                    x: 0,\n                    y: 0,\n                    opacity: 1,\n                    duration,\n                    delay,\n                    ease: \"power3.out\",\n                    scrollTrigger: {\n                        trigger: element,\n                        start: \"top 80%\",\n                        toggleActions: \"play none none reverse\"\n                    }\n                });\n            }\n        }\n    }[\"SmoothReveal.useEffect\"], [\n        direction,\n        delay,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\AdvancedParallax.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(SmoothReveal, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\n_c = SmoothReveal;\n// Staggered children animation\nfunction StaggerContainer(param) {\n    let { children, className = '', stagger = 0.1 } = param;\n    _s1();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StaggerContainer.useEffect\": ()=>{\n            if (ref.current) {\n                const children = ref.current.children;\n                gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(children, {\n                    y: 50,\n                    opacity: 0\n                }, {\n                    y: 0,\n                    opacity: 1,\n                    duration: 0.8,\n                    stagger,\n                    ease: \"power3.out\",\n                    scrollTrigger: {\n                        trigger: ref.current,\n                        start: \"top 80%\",\n                        toggleActions: \"play none none reverse\"\n                    }\n                });\n            }\n        }\n    }[\"StaggerContainer.useEffect\"], [\n        stagger\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\AdvancedParallax.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s1(StaggerContainer, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\n_c1 = StaggerContainer;\n// Morphing background effect\nfunction MorphingBackground(param) {\n    let { className = '', colors = [\n        '#10B981',\n        '#3B82F6',\n        '#8B5CF6'\n    ] } = param;\n    _s2();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MorphingBackground.useEffect\": ()=>{\n            if (ref.current) {\n                const tl = gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.timeline({\n                    repeat: -1\n                });\n                colors.forEach({\n                    \"MorphingBackground.useEffect\": (color, index)=>{\n                        tl.to(ref.current, {\n                            backgroundColor: color,\n                            duration: 3,\n                            ease: \"power2.inOut\"\n                        });\n                    }\n                }[\"MorphingBackground.useEffect\"]);\n            }\n        }\n    }[\"MorphingBackground.useEffect\"], [\n        colors\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: className\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\AdvancedParallax.tsx\",\n        lineNumber: 134,\n        columnNumber: 10\n    }, this);\n}\n_s2(MorphingBackground, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\n_c2 = MorphingBackground;\n// Text reveal with typewriter effect\nfunction TypewriterText(param) {\n    let { text, className = '', speed = 0.05 } = param;\n    _s3();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TypewriterText.useEffect\": ()=>{\n            if (ref.current) {\n                const element = ref.current;\n                element.textContent = '';\n                gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to({}, {\n                    duration: text.length * speed,\n                    ease: \"none\",\n                    onUpdate: {\n                        \"TypewriterText.useEffect\": function() {\n                            const progress = this.progress();\n                            const currentLength = Math.floor(progress * text.length);\n                            element.textContent = text.slice(0, currentLength);\n                        }\n                    }[\"TypewriterText.useEffect\"],\n                    scrollTrigger: {\n                        trigger: element,\n                        start: \"top 80%\",\n                        toggleActions: \"play none none reverse\"\n                    }\n                });\n            }\n        }\n    }[\"TypewriterText.useEffect\"], [\n        text,\n        speed\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: className\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\AdvancedParallax.tsx\",\n        lineNumber: 171,\n        columnNumber: 10\n    }, this);\n}\n_s3(TypewriterText, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\n_c3 = TypewriterText;\n// Parallax layers with different speeds\nfunction ParallaxLayers(param) {\n    let { layers, className = '' } = param;\n    _s4();\n    var _s = $RefreshSig$();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"relative \".concat(className),\n        children: layers.map(_s((layer, index)=>{\n            _s();\n            const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring)((0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useTransform)(scrollYProgress, [\n                0,\n                1\n            ], [\n                0,\n                -layer.speed * 100\n            ]), {\n                stiffness: 100,\n                damping: 30\n            });\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                style: {\n                    y\n                },\n                className: \"absolute inset-0 \".concat(layer.className || ''),\n                children: layer.content\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\AdvancedParallax.tsx\",\n                lineNumber: 201,\n                columnNumber: 11\n            }, this);\n        }, \"UV6C5DrmTrZonRQ/FKQfXWSXgr0=\", false, function() {\n            return [\n                framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring\n            ];\n        }))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\AdvancedParallax.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s4(ParallaxLayers, \"xX4DAprT77NLgRaInDg3PpWgeq4=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useScroll\n    ];\n});\n_c4 = ParallaxLayers;\n// 3D tilt effect on hover\nfunction TiltCard(param) {\n    let { children, className = '', maxTilt = 15 } = param;\n    _s5();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TiltCard.useEffect\": ()=>{\n            const element = ref.current;\n            if (!element) return;\n            const handleMouseMove = {\n                \"TiltCard.useEffect.handleMouseMove\": (e)=>{\n                    const rect = element.getBoundingClientRect();\n                    const x = e.clientX - rect.left;\n                    const y = e.clientY - rect.top;\n                    const centerX = rect.width / 2;\n                    const centerY = rect.height / 2;\n                    const rotateX = (y - centerY) / centerY * maxTilt;\n                    const rotateY = (centerX - x) / centerX * maxTilt;\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to(element, {\n                        rotateX,\n                        rotateY,\n                        duration: 0.3,\n                        ease: \"power2.out\",\n                        transformPerspective: 1000\n                    });\n                }\n            }[\"TiltCard.useEffect.handleMouseMove\"];\n            const handleMouseLeave = {\n                \"TiltCard.useEffect.handleMouseLeave\": ()=>{\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to(element, {\n                        rotateX: 0,\n                        rotateY: 0,\n                        duration: 0.5,\n                        ease: \"power2.out\"\n                    });\n                }\n            }[\"TiltCard.useEffect.handleMouseLeave\"];\n            element.addEventListener('mousemove', handleMouseMove);\n            element.addEventListener('mouseleave', handleMouseLeave);\n            return ({\n                \"TiltCard.useEffect\": ()=>{\n                    element.removeEventListener('mousemove', handleMouseMove);\n                    element.removeEventListener('mouseleave', handleMouseLeave);\n                }\n            })[\"TiltCard.useEffect\"];\n        }\n    }[\"TiltCard.useEffect\"], [\n        maxTilt\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: className,\n        style: {\n            transformStyle: 'preserve-3d'\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\AdvancedParallax.tsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, this);\n}\n_s5(TiltCard, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\n_c5 = TiltCard;\n// Scroll-triggered counter animation\nfunction AnimatedCounter(param) {\n    let { end, duration = 2, className = '' } = param;\n    _s6();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useInView)(ref, {\n        once: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnimatedCounter.useEffect\": ()=>{\n            if (isInView && ref.current) {\n                gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to({\n                    value: 0\n                }, {\n                    value: end,\n                    duration,\n                    ease: \"power2.out\",\n                    onUpdate: {\n                        \"AnimatedCounter.useEffect\": function() {\n                            if (ref.current) {\n                                ref.current.textContent = Math.floor(this.targets()[0].value).toString();\n                            }\n                        }\n                    }[\"AnimatedCounter.useEffect\"]\n                });\n            }\n        }\n    }[\"AnimatedCounter.useEffect\"], [\n        isInView,\n        end,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        ref: ref,\n        className: className,\n        children: \"0\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\AdvancedParallax.tsx\",\n        lineNumber: 301,\n        columnNumber: 10\n    }, this);\n}\n_s6(AnimatedCounter, \"aIj2rGFwktnr9pBb+biOnOSDeRU=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_8__.useInView\n    ];\n});\n_c6 = AnimatedCounter;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"SmoothReveal\");\n$RefreshReg$(_c1, \"StaggerContainer\");\n$RefreshReg$(_c2, \"MorphingBackground\");\n$RefreshReg$(_c3, \"TypewriterText\");\n$RefreshReg$(_c4, \"ParallaxLayers\");\n$RefreshReg$(_c5, \"TiltCard\");\n$RefreshReg$(_c6, \"AnimatedCounter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/animations/AdvancedParallax.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/animations/index.ts":
/*!********************************************!*\
  !*** ./src/components/animations/index.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedCounter: () => (/* reexport safe */ _AdvancedParallax__WEBPACK_IMPORTED_MODULE_3__.AnimatedCounter),\n/* harmony export */   FadeIn: () => (/* reexport safe */ _MotionWrapper__WEBPACK_IMPORTED_MODULE_1__.FadeIn),\n/* harmony export */   FloatingElement: () => (/* reexport safe */ _ParallaxElement__WEBPACK_IMPORTED_MODULE_2__.FloatingElement),\n/* harmony export */   MagneticElement: () => (/* reexport safe */ _ParallaxElement__WEBPACK_IMPORTED_MODULE_2__.MagneticElement),\n/* harmony export */   MorphingBackground: () => (/* reexport safe */ _AdvancedParallax__WEBPACK_IMPORTED_MODULE_3__.MorphingBackground),\n/* harmony export */   MotionWrapper: () => (/* reexport safe */ _MotionWrapper__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ParallaxBackground: () => (/* reexport safe */ _ParallaxElement__WEBPACK_IMPORTED_MODULE_2__.ParallaxBackground),\n/* harmony export */   ParallaxCard: () => (/* reexport safe */ _ParallaxElement__WEBPACK_IMPORTED_MODULE_2__.ParallaxCard),\n/* harmony export */   ParallaxElement: () => (/* reexport safe */ _ParallaxElement__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ParallaxImage: () => (/* reexport safe */ _ParallaxElement__WEBPACK_IMPORTED_MODULE_2__.ParallaxImage),\n/* harmony export */   ParallaxLayers: () => (/* reexport safe */ _AdvancedParallax__WEBPACK_IMPORTED_MODULE_3__.ParallaxLayers),\n/* harmony export */   ParallaxText: () => (/* reexport safe */ _ParallaxElement__WEBPACK_IMPORTED_MODULE_2__.ParallaxText),\n/* harmony export */   ScaleIn: () => (/* reexport safe */ _MotionWrapper__WEBPACK_IMPORTED_MODULE_1__.ScaleIn),\n/* harmony export */   SlideLeft: () => (/* reexport safe */ _MotionWrapper__WEBPACK_IMPORTED_MODULE_1__.SlideLeft),\n/* harmony export */   SlideUp: () => (/* reexport safe */ _MotionWrapper__WEBPACK_IMPORTED_MODULE_1__.SlideUp),\n/* harmony export */   SmoothReveal: () => (/* reexport safe */ _AdvancedParallax__WEBPACK_IMPORTED_MODULE_3__.SmoothReveal),\n/* harmony export */   SmoothScrollProvider: () => (/* reexport safe */ _SmoothScrollProvider__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   StaggerContainer: () => (/* reexport safe */ _AdvancedParallax__WEBPACK_IMPORTED_MODULE_3__.StaggerContainer),\n/* harmony export */   TiltCard: () => (/* reexport safe */ _AdvancedParallax__WEBPACK_IMPORTED_MODULE_3__.TiltCard),\n/* harmony export */   TypewriterText: () => (/* reexport safe */ _AdvancedParallax__WEBPACK_IMPORTED_MODULE_3__.TypewriterText)\n/* harmony export */ });\n/* harmony import */ var _SmoothScrollProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SmoothScrollProvider */ \"(app-pages-browser)/./src/components/animations/SmoothScrollProvider.tsx\");\n/* harmony import */ var _MotionWrapper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MotionWrapper */ \"(app-pages-browser)/./src/components/animations/MotionWrapper.tsx\");\n/* harmony import */ var _ParallaxElement__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ParallaxElement */ \"(app-pages-browser)/./src/components/animations/ParallaxElement.tsx\");\n/* harmony import */ var _AdvancedParallax__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AdvancedParallax */ \"(app-pages-browser)/./src/components/animations/AdvancedParallax.tsx\");\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FuaW1hdGlvbnMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXlFO0FBQ3VCO0FBU3JFO0FBU0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYXJraXRcXERlc2t0b3BcXGNvb2tpZXNcXHBvcnRmb2xpb1xcc3JjXFxjb21wb25lbnRzXFxhbmltYXRpb25zXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IGFzIFNtb290aFNjcm9sbFByb3ZpZGVyIH0gZnJvbSAnLi9TbW9vdGhTY3JvbGxQcm92aWRlcic7XG5leHBvcnQgeyBkZWZhdWx0IGFzIE1vdGlvbldyYXBwZXIsIEZhZGVJbiwgU2xpZGVVcCwgU2xpZGVMZWZ0LCBTY2FsZUluIH0gZnJvbSAnLi9Nb3Rpb25XcmFwcGVyJztcbmV4cG9ydCB7XG4gIGRlZmF1bHQgYXMgUGFyYWxsYXhFbGVtZW50LFxuICBQYXJhbGxheEltYWdlLFxuICBQYXJhbGxheFRleHQsXG4gIFBhcmFsbGF4Q2FyZCxcbiAgUGFyYWxsYXhCYWNrZ3JvdW5kLFxuICBGbG9hdGluZ0VsZW1lbnQsXG4gIE1hZ25ldGljRWxlbWVudFxufSBmcm9tICcuL1BhcmFsbGF4RWxlbWVudCc7XG5leHBvcnQge1xuICBTbW9vdGhSZXZlYWwsXG4gIFN0YWdnZXJDb250YWluZXIsXG4gIE1vcnBoaW5nQmFja2dyb3VuZCxcbiAgVHlwZXdyaXRlclRleHQsXG4gIFBhcmFsbGF4TGF5ZXJzLFxuICBUaWx0Q2FyZCxcbiAgQW5pbWF0ZWRDb3VudGVyXG59IGZyb20gJy4vQWR2YW5jZWRQYXJhbGxheCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIlNtb290aFNjcm9sbFByb3ZpZGVyIiwiTW90aW9uV3JhcHBlciIsIkZhZGVJbiIsIlNsaWRlVXAiLCJTbGlkZUxlZnQiLCJTY2FsZUluIiwiUGFyYWxsYXhFbGVtZW50IiwiUGFyYWxsYXhJbWFnZSIsIlBhcmFsbGF4VGV4dCIsIlBhcmFsbGF4Q2FyZCIsIlBhcmFsbGF4QmFja2dyb3VuZCIsIkZsb2F0aW5nRWxlbWVudCIsIk1hZ25ldGljRWxlbWVudCIsIlNtb290aFJldmVhbCIsIlN0YWdnZXJDb250YWluZXIiLCJNb3JwaGluZ0JhY2tncm91bmQiLCJUeXBld3JpdGVyVGV4dCIsIlBhcmFsbGF4TGF5ZXJzIiwiVGlsdENhcmQiLCJBbmltYXRlZENvdW50ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/animations/index.ts\n"));

/***/ })

});