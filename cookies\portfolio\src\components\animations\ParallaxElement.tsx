"use client";

import { useEffect, useRef } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface ParallaxElementProps {
  children: React.ReactNode;
  speed?: number;
  className?: string;
  direction?: 'up' | 'down';
  type?: 'framer' | 'gsap';
  scale?: boolean;
  rotate?: boolean;
  opacity?: boolean;
}

export default function ParallaxElement({
  children,
  speed = 0.5,
  className = '',
  direction = 'up',
  type = 'framer',
  scale = false,
  rotate = false,
  opacity = false
}: ParallaxElementProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  // Framer Motion transforms with spring physics
  const y = useSpring(
    useTransform(
      scrollYProgress,
      [0, 1],
      direction === 'up' ? [0, -speed * 100] : [0, speed * 100]
    ),
    { stiffness: 100, damping: 30, restDelta: 0.001 }
  );

  const scaleValue = useSpring(
    useTransform(scrollYProgress, [0, 0.5, 1], [0.8, 1.1, 0.8]),
    { stiffness: 100, damping: 30 }
  );

  const rotateValue = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, 360 * speed]),
    { stiffness: 100, damping: 30 }
  );

  const opacityValue = useSpring(
    useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]),
    { stiffness: 100, damping: 30 }
  );

  // GSAP parallax effect
  useEffect(() => {
    if (type === 'gsap' && ref.current) {
      const element = ref.current;

      gsap.fromTo(element,
        {
          y: direction === 'up' ? 100 * speed : -100 * speed,
          scale: scale ? 0.8 : 1,
          rotation: rotate ? -10 : 0,
          opacity: opacity ? 0 : 1,
        },
        {
          y: direction === 'up' ? -100 * speed : 100 * speed,
          scale: scale ? 1.1 : 1,
          rotation: rotate ? 10 : 0,
          opacity: opacity ? 1 : 1,
          ease: "none",
          scrollTrigger: {
            trigger: element,
            start: "top bottom",
            end: "bottom top",
            scrub: 1,
            invalidateOnRefresh: true,
          }
        }
      );

      return () => {
        ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      };
    }
  }, [type, speed, direction, scale, rotate, opacity]);

  if (type === 'gsap') {
    return (
      <div ref={ref} className={className}>
        {children}
      </div>
    );
  }

  return (
    <motion.div
      ref={ref}
      style={{
        y,
        scale: scale ? scaleValue : 1,
        rotate: rotate ? rotateValue : 0,
        opacity: opacity ? opacityValue : 1
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Specialized parallax components
export function ParallaxImage({
  src,
  alt,
  className = '',
  speed = 0.5,
  type = 'framer',
  scale = false,
  rotate = false
}: {
  src: string;
  alt: string;
  className?: string;
  speed?: number;
  type?: 'framer' | 'gsap';
  scale?: boolean;
  rotate?: boolean;
}) {
  return (
    <ParallaxElement speed={speed} className={className} type={type} scale={scale} rotate={rotate}>
      <img src={src} alt={alt} className="w-full h-full object-cover" />
    </ParallaxElement>
  );
}

export function ParallaxText({
  children,
  className = '',
  speed = 0.3,
  type = 'framer',
  opacity = false
}: {
  children: React.ReactNode;
  className?: string;
  speed?: number;
  type?: 'framer' | 'gsap';
  opacity?: boolean;
}) {
  return (
    <ParallaxElement speed={speed} className={className} type={type} opacity={opacity}>
      {children}
    </ParallaxElement>
  );
}

// Advanced parallax components
export function ParallaxCard({
  children,
  className = '',
  speed = 0.4,
  type = 'framer'
}: {
  children: React.ReactNode;
  className?: string;
  speed?: number;
  type?: 'framer' | 'gsap';
}) {
  return (
    <ParallaxElement
      speed={speed}
      className={className}
      type={type}
      scale={true}
      opacity={true}
    >
      {children}
    </ParallaxElement>
  );
}

export function ParallaxBackground({
  children,
  className = '',
  speed = 0.8,
  direction = 'up' as const
}: {
  children: React.ReactNode;
  className?: string;
  speed?: number;
  direction?: 'up' | 'down';
}) {
  return (
    <ParallaxElement
      speed={speed}
      className={className}
      direction={direction}
      type="gsap"
    >
      {children}
    </ParallaxElement>
  );
}

// Floating animation component
export function FloatingElement({
  children,
  className = '',
  intensity = 1
}: {
  children: React.ReactNode;
  className?: string;
  intensity?: number;
}) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      gsap.to(ref.current, {
        y: `+=${20 * intensity}`,
        duration: 3,
        ease: "power2.inOut",
        yoyo: true,
        repeat: -1
      });
    }
  }, [intensity]);

  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  );
}

// Magnetic hover effect
export function MagneticElement({
  children,
  className = '',
  strength = 0.3
}: {
  children: React.ReactNode;
  className?: string;
  strength?: number;
}) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;

      gsap.to(element, {
        x: x * strength,
        y: y * strength,
        duration: 0.3,
        ease: "power2.out"
      });
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        x: 0,
        y: 0,
        duration: 0.5,
        ease: "elastic.out(1, 0.3)"
      });
    };

    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [strength]);

  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  );
}
