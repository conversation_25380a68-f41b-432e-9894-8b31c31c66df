"use client";

import Image from "next/image";
import { useState, useEffect } from "react";
import { Calendar, GitBranch, Star, Code, ExternalLink, Loader } from "lucide-react";

export default function ContributionSection() {
  const [userStats, setUserStats] = useState(null);
  const [repositories, setRepositories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const username = 'Arkit-k';

  useEffect(() => {
    const fetchGitHubData = async () => {
      try {
        setLoading(true);
        
        // Fetch user data
        const userResponse = await fetch(`https://api.github.com/users/${username}`);
        if (!userResponse.ok) throw new Error('Failed to fetch user data');
        const userData = await userResponse.json();
        
        // Fetch repositories
        const reposResponse = await fetch(`https://api.github.com/users/${username}/repos?sort=updated&per_page=6`);
        if (!reposResponse.ok) throw new Error('Failed to fetch repositories');
        const reposData = await reposResponse.json();
        
        setUserStats(userData);
        setRepositories(reposData);
        
      } catch (err) {
        setError(err.message);
        console.error('GitHub API Error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchGitHubData();
  }, []);

  const getLanguageColor = (language) => {
    const colors = {
      'JavaScript': 'bg-yellow-400',
      'TypeScript': 'bg-blue-600',
      'Python': 'bg-blue-500',
      'HTML': 'bg-orange-600',
      'CSS': 'bg-purple-600',
      'Java': 'bg-red-600',
      'C++': 'bg-pink-600',
      'C': 'bg-gray-600',
      'Rust': 'bg-orange-800',
      'Go': 'bg-cyan-500'
    };
    return colors[language] || 'bg-gray-500';
  };

  return (
    <section className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="bg-deep-charcoal dark:bg-dark-surface rounded-2xl p-8 lg:p-12">
          
          {/* Header Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-3xl lg:text-4xl font-bold text-white mb-2">
                  GitHub Activity
                </h2>
                <p className="text-gray-400 text-lg">
                  My coding journey and contributions over time
                </p>
              </div>
              <a 
                href={`https://github.com/${username}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 px-6 py-3 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg transition-colors duration-200"
              >
                <ExternalLink className="w-5 h-5" />
                View Profile
              </a>
            </div>

            {/* Stats Cards */}
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader className="w-8 h-8 animate-spin text-emerald-500 mr-3" />
                <span className="text-gray-400">Loading GitHub stats...</span>
              </div>
            ) : error ? (
              <div className="text-red-400 text-center py-4">
                <p>Unable to load GitHub stats</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                <div className="bg-gray-800/50 rounded-xl p-6 text-center border border-gray-700">
                  <Code className="w-8 h-8 text-emerald-400 mx-auto mb-3" />
                  <div className="text-2xl font-bold text-emerald-400">{userStats?.public_repos || 0}</div>
                  <div className="text-sm text-gray-400">Public Repos</div>
                </div>
                <div className="bg-gray-800/50 rounded-xl p-6 text-center border border-gray-700">
                  <GitBranch className="w-8 h-8 text-blue-400 mx-auto mb-3" />
                  <div className="text-2xl font-bold text-blue-400">{userStats?.followers || 0}</div>
                  <div className="text-sm text-gray-400">Followers</div>
                </div>
                <div className="bg-gray-800/50 rounded-xl p-6 text-center border border-gray-700">
                  <Star className="w-8 h-8 text-purple-400 mx-auto mb-3" />
                  <div className="text-2xl font-bold text-purple-400">
                    {repositories.reduce((sum, repo) => sum + repo.stargazers_count, 0)}
                  </div>
                  <div className="text-sm text-gray-400">Total Stars</div>
                </div>
                <div className="bg-gray-800/50 rounded-xl p-6 text-center border border-gray-700">
                  <Calendar className="w-8 h-8 text-yellow-400 mx-auto mb-3" />
                  <div className="text-2xl font-bold text-yellow-400">{userStats?.following || 0}</div>
                  <div className="text-sm text-gray-400">Following</div>
                </div>
              </div>
            )}
          </div>

          {/* Contribution Graph */}
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Calendar className="w-6 h-6 text-emerald-400" />
              Contribution Activity
            </h3>
            <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
              <Image
                src="https://github-readme-activity-graph.vercel.app/graph?username=Arkit-k&bg_color=1f2937&color=10b981&line=10b981&point=10b981&area=true&hide_border=true&custom_title=GitHub%20Contribution%20Graph"
                alt="GitHub Contribution Graph for Arkit-k"
                width={1200}
                height={400}
                className="w-full rounded-lg"
                priority={false}
                unoptimized
                onError={() => setError('Failed to load contribution graph')}
              />
            </div>
          </div>

          {/* GitHub Stats Images */}
          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
              <Image
                src={`https://github-readme-stats.vercel.app/api?username=${username}&show_icons=true&theme=dark&bg_color=1f2937&title_color=10b981&text_color=f3f4f6&icon_color=10b981&border_color=374151`}
                alt="GitHub Stats for Arkit-k"
                width={500}
                height={200}
                className="w-full rounded-lg"
                unoptimized
              />
            </div>
            <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
              <Image
                src={`https://github-readme-streak-stats.herokuapp.com/?user=${username}&theme=dark&background=1f2937&ring=10b981&fire=10b981&currStreakLabel=10b981&sideLabels=f3f4f6&currStreakNum=f3f4f6&sideNums=f3f4f6&dates=9ca3af&border=374151`}
                alt="GitHub Streak for Arkit-k"
                width={500}
                height={200}
                className="w-full rounded-lg"
                unoptimized
              />
            </div>
          </div>

          {/* Recent Repositories */}
          {!loading && !error && repositories.length > 0 && (
            <div>
              <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
                <Code className="w-6 h-6 text-blue-400" />
                Featured Repositories
              </h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {repositories.slice(0, 6).map((repo) => (
                  <a
                    key={repo.id}
                    href={repo.html_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block bg-gray-800/50 rounded-xl p-6 border border-gray-700 hover:border-emerald-500 hover:bg-gray-800/70 transition-all duration-200"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-emerald-400 truncate text-lg">
                        {repo.name}
                      </h4>
                      <div className="flex items-center gap-1 text-sm text-gray-400 ml-2">
                        <Star className="w-4 h-4" />
                        {repo.stargazers_count}
                      </div>
                    </div>
                    
                    {repo.description && (
                      <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                        {repo.description}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between text-sm">
                      {repo.language && (
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${getLanguageColor(repo.language)}`}></div>
                          <span className="text-gray-400">{repo.language}</span>
                        </div>
                      )}
                      <span className="text-gray-500 text-xs">
                        Updated {new Date(repo.updated_at).toLocaleDateString()}
                      </span>
                    </div>
                  </a>
                ))}
              </div>
            </div>
          )}

          {/* Languages Stats */}
          <div className="mt-8">
            <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
              <Image
                src={`https://github-readme-stats.vercel.app/api/top-langs/?username=${username}&layout=compact&theme=dark&bg_color=1f2937&title_color=10b981&text_color=f3f4f6&border_color=374151`}
                alt="Most Used Languages"
                width={600}
                height={300}
                className="w-full max-w-md mx-auto rounded-lg"
                unoptimized
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}