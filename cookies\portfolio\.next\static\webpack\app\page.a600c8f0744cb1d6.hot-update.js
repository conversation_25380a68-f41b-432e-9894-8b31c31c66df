"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/HeroSection.tsx":
/*!*************************************************!*\
  !*** ./src/components/sections/HeroSection.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_animations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/animations */ \"(app-pages-browser)/./src/components/animations/index.ts\");\n\n\n\n// GitHub Icon Component\nconst GitHubIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n            lineNumber: 13,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined);\n_c = GitHubIcon;\n// X (Twitter) Icon Component\nconst XIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n            lineNumber: 20,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, undefined);\n_c1 = XIcon;\n// LinkedIn Icon Component\nconst LinkedInIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n            lineNumber: 27,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined);\n_c2 = LinkedInIcon;\n// Instagram Icon Component\nconst InstagramIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined);\n_c3 = InstagramIcon;\nfunction HeroSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"px-6 lg:px-12 py-16 lg:py-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations__WEBPACK_IMPORTED_MODULE_2__.SmoothReveal, {\n                                direction: \"up\",\n                                delay: 0.2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-6xl font-bold text-deep-charcoal dark:text-dark-text leading-tight\",\n                                            children: [\n                                                \"Software Engineer, Writer\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-deep-charcoal/80 dark:text-dark-text/80 max-w-xl leading-relaxed\",\n                                            children: \"I'm Arkit karmokar, an experienced Full Stack Engineer passionate about learning and building innovative software solutions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations__WEBPACK_IMPORTED_MODULE_2__.StaggerContainer, {\n                                className: \"flex items-center space-x-6\",\n                                stagger: 0.1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations__WEBPACK_IMPORTED_MODULE_2__.MagneticElement, {\n                                        strength: 0.2,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://github.com/Arkit-k\",\n                                            className: \"flex items-center space-x-2 text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GitHubIcon, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"GitHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations__WEBPACK_IMPORTED_MODULE_2__.MagneticElement, {\n                                        strength: 0.2,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://x.com/arkit_k\",\n                                            className: \"flex items-center space-x-2 text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(XIcon, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"X\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations__WEBPACK_IMPORTED_MODULE_2__.MagneticElement, {\n                                        strength: 0.2,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://linkedin.com/in/arkit-karmokar\",\n                                            className: \"flex items-center space-x-2 text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LinkedInIcon, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"LinkedIn\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex justify-center lg:justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations__WEBPACK_IMPORTED_MODULE_2__.FloatingElement, {\n                            intensity: 1.5,\n                            className: \"w-80 h-96 lg:w-100 lg:h-[500px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations__WEBPACK_IMPORTED_MODULE_2__.ParallaxElement, {\n                                speed: 0.3,\n                                direction: \"down\",\n                                scale: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/kioske.png\",\n                                        alt: \"Character illustration showing a person with glasses making a peace sign\",\n                                        fill: true,\n                                        className: \"object-contain\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_c4 = HeroSection;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"GitHubIcon\");\n$RefreshReg$(_c1, \"XIcon\");\n$RefreshReg$(_c2, \"LinkedInIcon\");\n$RefreshReg$(_c3, \"InstagramIcon\");\n$RefreshReg$(_c4, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/HeroSection.tsx\n"));

/***/ })

});