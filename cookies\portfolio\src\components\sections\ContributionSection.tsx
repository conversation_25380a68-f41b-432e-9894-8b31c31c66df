"use client";

import Image from "next/image";
import { ExternalLink, GitBranch, Star, Code2 } from "lucide-react";

export default function ContributionSection() {
  const username = 'Arkit-k';

  return (
    <section className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="bg-gradient-to-br from-deep-charcoal via-gray-900 to-deep-charcoal dark:from-dark-surface dark:via-gray-800 dark:to-dark-surface rounded-3xl p-8 lg:p-12 border border-gray-700/50 backdrop-blur-sm shadow-2xl">

          {/* Header Section */}
          <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-10 gap-6">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-emerald-500/10 rounded-xl border border-emerald-500/20">
                  <Code2 className="w-6 h-6 text-emerald-400" />
                </div>
                <h2 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
                  GitHub Contributions
                </h2>
              </div>
              <p className="text-gray-400 text-lg max-w-md leading-relaxed">
                Explore my coding journey through commits, contributions, and project activity
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="hidden sm:flex items-center gap-4 text-sm text-gray-400">
                <div className="flex items-center gap-2">
                  <GitBranch className="w-4 h-4" />
                  <span>Active Developer</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4" />
                  <span>Open Source</span>
                </div>
              </div>
              
              <a
                href={`https://github.com/${username}`}
                target="_blank"
                rel="noopener noreferrer"
                className="group flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-emerald-500/25 hover:scale-105 active:scale-95"
              >
                <ExternalLink className="w-5 h-5 transition-transform group-hover:rotate-12" />
                <span className="font-medium">View Profile</span>
              </a>
            </div>
          </div>

          {/* Contribution Graph Container */}
          <div className="relative">
            {/* Background decoration */}
            <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500/20 via-blue-500/20 to-purple-500/20 rounded-2xl blur opacity-30"></div>
            
            {/* Main graph container */}
            <div className="relative bg-gray-900/80 backdrop-blur-sm rounded-2xl p-6 lg:p-8 border border-gray-700/50 overflow-hidden">
              {/* Subtle grid pattern overlay */}
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.03)_1px,transparent_0)] [background-size:20px_20px] pointer-events-none"></div>
              
              {/* Graph title */}
              <div className="relative mb-6">
                <h3 className="text-lg font-semibold text-gray-200 mb-2">
                  Contribution Activity
                </h3>
                <div className="flex items-center gap-4 text-sm text-gray-400">
                  <span>Past 12 months</span>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-emerald-500 rounded-sm"></div>
                    <span>Active days</span>
                  </div>
                </div>
              </div>
              
              {/* Contribution Graph */}
              <div className="relative bg-black/20 rounded-xl p-4 border border-gray-800/50">
                <Image
                  src={`https://ghchart.rshah.org/${username}`}
                  alt={`${username}'s GitHub Contribution Graph`}
                  width={1000}
                  height={200}
                  className="w-full rounded-lg opacity-90 hover:opacity-100 transition-opacity duration-300"
                  unoptimized
                />
                
                {/* Loading state overlay */}
                <div className="absolute inset-0 bg-gray-800/50 rounded-lg flex items-center justify-center opacity-0 hover:opacity-0 transition-opacity">
                  <div className="text-gray-400 text-sm">Loading contributions...</div>
                </div>
              </div>
              
              {/* Footer info */}
              <div className="mt-4 flex flex-col sm:flex-row sm:items-center justify-between gap-3 text-sm text-gray-500">
                <p>Data provided by GitHub's public API</p>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                  <span>Live data</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}