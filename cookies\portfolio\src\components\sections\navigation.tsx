import Link from "next/link";
import Image from "next/image";
import { ThemeToggle } from "@/components/theme-toggle";

export function Navigation() {
  return (
    <nav className="flex items-center justify-between px-8 py-6 lg:px-16">
      {/* Logo */}
      <Link href="/" className="flex items-center space-x-3 text-deep-charcoal dark:text-dark-text hover:opacity-80 transition-opacity">
        <div className="w-10 h-10 relative">
          <Image
            src="/arkit-logo.png"
            alt="Arkit_k Logo"
            fill
            className="object-contain rounded-full"
          />
        </div>
        <span className="text-xl font-bold">Arkit_k</span>
      </Link>

      {/* Navigation Links */}
      <div className="hidden md:flex items-center space-x-8 text-deep-charcoal dark:text-dark-text">
        <Link href="#about" className="hover:text-accent-green transition-colors">
          About
        </Link>
        <Link href="#projects" className="hover:text-accent-green transition-colors">
          Projects
        </Link>
        <Link href="#blog" className="hover:text-accent-green transition-colors">
          Blog
        </Link>
        <Link href="#photos" className="hover:text-accent-green transition-colors">
          Photos
        </Link>
      </div>

      {/* Theme Toggle */}
      <ThemeToggle />
    </nav>
  );
}
