"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/animations/ParallaxElement.tsx":
/*!*******************************************************!*\
  !*** ./src/components/animations/ParallaxElement.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingElement: () => (/* binding */ FloatingElement),\n/* harmony export */   MagneticElement: () => (/* binding */ MagneticElement),\n/* harmony export */   ParallaxBackground: () => (/* binding */ ParallaxBackground),\n/* harmony export */   ParallaxCard: () => (/* binding */ ParallaxCard),\n/* harmony export */   ParallaxImage: () => (/* binding */ ParallaxImage),\n/* harmony export */   ParallaxText: () => (/* binding */ ParallaxText),\n/* harmony export */   \"default\": () => (/* binding */ ParallaxElement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ default,ParallaxImage,ParallaxText,ParallaxCard,ParallaxBackground,FloatingElement,MagneticElement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n// Register GSAP plugins\nif (true) {\n    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\n}\nfunction ParallaxElement(param) {\n    let { children, speed = 0.5, className = '', direction = 'up', type = 'framer', scale = false, rotate = false, opacity = false } = param;\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useScroll)({\n        target: ref,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    // Framer Motion transforms with spring physics\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring)((0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], direction === 'up' ? [\n        0,\n        -speed * 100\n    ] : [\n        0,\n        speed * 100\n    ]), {\n        stiffness: 100,\n        damping: 30,\n        restDelta: 0.001\n    });\n    const scaleValue = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring)((0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useTransform)(scrollYProgress, [\n        0,\n        0.5,\n        1\n    ], [\n        0.8,\n        1.1,\n        0.8\n    ]), {\n        stiffness: 100,\n        damping: 30\n    });\n    const rotateValue = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring)((0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        360 * speed\n    ]), {\n        stiffness: 100,\n        damping: 30\n    });\n    const opacityValue = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring)((0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useTransform)(scrollYProgress, [\n        0,\n        0.2,\n        0.8,\n        1\n    ], [\n        0,\n        1,\n        1,\n        0\n    ]), {\n        stiffness: 100,\n        damping: 30\n    });\n    // GSAP parallax effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParallaxElement.useEffect\": ()=>{\n            if (type === 'gsap' && ref.current) {\n                const element = ref.current;\n                gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(element, {\n                    y: direction === 'up' ? 100 * speed : -100 * speed,\n                    scale: scale ? 0.8 : 1,\n                    rotation: rotate ? -10 : 0,\n                    opacity: opacity ? 0 : 1\n                }, {\n                    y: direction === 'up' ? -100 * speed : 100 * speed,\n                    scale: scale ? 1.1 : 1,\n                    rotation: rotate ? 10 : 0,\n                    opacity: opacity ? 1 : 1,\n                    ease: \"none\",\n                    scrollTrigger: {\n                        trigger: element,\n                        start: \"top bottom\",\n                        end: \"bottom top\",\n                        scrub: 1,\n                        invalidateOnRefresh: true\n                    }\n                });\n                return ({\n                    \"ParallaxElement.useEffect\": ()=>{\n                        gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger.getAll().forEach({\n                            \"ParallaxElement.useEffect\": (trigger)=>trigger.kill()\n                        }[\"ParallaxElement.useEffect\"]);\n                    }\n                })[\"ParallaxElement.useEffect\"];\n            }\n        }\n    }[\"ParallaxElement.useEffect\"], [\n        type,\n        speed,\n        direction,\n        scale,\n        rotate,\n        opacity\n    ]);\n    if (type === 'gsap') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: ref,\n            className: className,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        ref: ref,\n        style: {\n            y,\n            scale: scale ? scaleValue : 1,\n            rotate: rotate ? rotateValue : 0,\n            opacity: opacity ? opacityValue : 1\n        },\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(ParallaxElement, \"khp6KH5wiM7dfg8g1oeRgemi9S0=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring\n    ];\n});\n_c = ParallaxElement;\n// Specialized parallax components\nfunction ParallaxImage(param) {\n    let { src, alt, className = '', speed = 0.5, type = 'framer', scale = false, rotate = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParallaxElement, {\n        speed: speed,\n        className: className,\n        type: type,\n        scale: scale,\n        rotate: rotate,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: src,\n            alt: alt,\n            className: \"w-full h-full object-cover\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ParallaxImage;\nfunction ParallaxText(param) {\n    let { children, className = '', speed = 0.3, type = 'framer', opacity = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParallaxElement, {\n        speed: speed,\n        className: className,\n        type: type,\n        opacity: opacity,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ParallaxText;\n// Advanced parallax components\nfunction ParallaxCard(param) {\n    let { children, className = '', speed = 0.4, type = 'framer' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParallaxElement, {\n        speed: speed,\n        className: className,\n        type: type,\n        scale: true,\n        opacity: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_c3 = ParallaxCard;\nfunction ParallaxBackground(param) {\n    let { children, className = '', speed = 0.8, direction = 'up' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParallaxElement, {\n        speed: speed,\n        className: className,\n        direction: direction,\n        type: \"gsap\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, this);\n}\n_c4 = ParallaxBackground;\n// Floating animation component\nfunction FloatingElement(param) {\n    let { children, className = '', intensity = 1 } = param;\n    _s1();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FloatingElement.useEffect\": ()=>{\n            if (ref.current) {\n                gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to(ref.current, {\n                    y: \"+=\".concat(20 * intensity),\n                    duration: 3,\n                    ease: \"power2.inOut\",\n                    yoyo: true,\n                    repeat: -1\n                });\n            }\n        }\n    }[\"FloatingElement.useEffect\"], [\n        intensity\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n        lineNumber: 241,\n        columnNumber: 5\n    }, this);\n}\n_s1(FloatingElement, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\n_c5 = FloatingElement;\n// Magnetic hover effect\nfunction MagneticElement(param) {\n    let { children, className = '', strength = 0.3 } = param;\n    _s2();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MagneticElement.useEffect\": ()=>{\n            const element = ref.current;\n            if (!element) return;\n            const handleMouseMove = {\n                \"MagneticElement.useEffect.handleMouseMove\": (e)=>{\n                    const rect = element.getBoundingClientRect();\n                    const x = e.clientX - rect.left - rect.width / 2;\n                    const y = e.clientY - rect.top - rect.height / 2;\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to(element, {\n                        x: x * strength,\n                        y: y * strength,\n                        duration: 0.3,\n                        ease: \"power2.out\"\n                    });\n                }\n            }[\"MagneticElement.useEffect.handleMouseMove\"];\n            const handleMouseLeave = {\n                \"MagneticElement.useEffect.handleMouseLeave\": ()=>{\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to(element, {\n                        x: 0,\n                        y: 0,\n                        duration: 0.5,\n                        ease: \"elastic.out(1, 0.3)\"\n                    });\n                }\n            }[\"MagneticElement.useEffect.handleMouseLeave\"];\n            element.addEventListener('mousemove', handleMouseMove);\n            element.addEventListener('mouseleave', handleMouseLeave);\n            return ({\n                \"MagneticElement.useEffect\": ()=>{\n                    element.removeEventListener('mousemove', handleMouseMove);\n                    element.removeEventListener('mouseleave', handleMouseLeave);\n                }\n            })[\"MagneticElement.useEffect\"];\n        }\n    }[\"MagneticElement.useEffect\"], [\n        strength\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, this);\n}\n_s2(MagneticElement, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\n_c6 = MagneticElement;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"ParallaxElement\");\n$RefreshReg$(_c1, \"ParallaxImage\");\n$RefreshReg$(_c2, \"ParallaxText\");\n$RefreshReg$(_c3, \"ParallaxCard\");\n$RefreshReg$(_c4, \"ParallaxBackground\");\n$RefreshReg$(_c5, \"FloatingElement\");\n$RefreshReg$(_c6, \"MagneticElement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FuaW1hdGlvbnMvUGFyYWxsYXhFbGVtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMEM7QUFDaUM7QUFDL0M7QUFDdUI7QUFFbkQsd0JBQXdCO0FBQ3hCLElBQUksSUFBNkIsRUFBRTtJQUNqQ00sc0NBQUlBLENBQUNFLGNBQWMsQ0FBQ0QsNkRBQWFBO0FBQ25DO0FBYWUsU0FBU0UsZ0JBQWdCLEtBU2pCO1FBVGlCLEVBQ3RDQyxRQUFRLEVBQ1JDLFFBQVEsR0FBRyxFQUNYQyxZQUFZLEVBQUUsRUFDZEMsWUFBWSxJQUFJLEVBQ2hCQyxPQUFPLFFBQVEsRUFDZkMsUUFBUSxLQUFLLEVBQ2JDLFNBQVMsS0FBSyxFQUNkQyxVQUFVLEtBQUssRUFDTSxHQVRpQjs7SUFVdEMsTUFBTUMsTUFBTWpCLDZDQUFNQSxDQUFpQjtJQUNuQyxNQUFNLEVBQUVrQixlQUFlLEVBQUUsR0FBR2hCLHdEQUFTQSxDQUFDO1FBQ3BDaUIsUUFBUUY7UUFDUkcsUUFBUTtZQUFDO1lBQWE7U0FBWTtJQUNwQztJQUVBLCtDQUErQztJQUMvQyxNQUFNQyxJQUFJakIsd0RBQVNBLENBQ2pCRCwyREFBWUEsQ0FDVmUsaUJBQ0E7UUFBQztRQUFHO0tBQUUsRUFDTk4sY0FBYyxPQUFPO1FBQUM7UUFBRyxDQUFDRixRQUFRO0tBQUksR0FBRztRQUFDO1FBQUdBLFFBQVE7S0FBSSxHQUUzRDtRQUFFWSxXQUFXO1FBQUtDLFNBQVM7UUFBSUMsV0FBVztJQUFNO0lBR2xELE1BQU1DLGFBQWFyQix3REFBU0EsQ0FDMUJELDJEQUFZQSxDQUFDZSxpQkFBaUI7UUFBQztRQUFHO1FBQUs7S0FBRSxFQUFFO1FBQUM7UUFBSztRQUFLO0tBQUksR0FDMUQ7UUFBRUksV0FBVztRQUFLQyxTQUFTO0lBQUc7SUFHaEMsTUFBTUcsY0FBY3RCLHdEQUFTQSxDQUMzQkQsMkRBQVlBLENBQUNlLGlCQUFpQjtRQUFDO1FBQUc7S0FBRSxFQUFFO1FBQUM7UUFBRyxNQUFNUjtLQUFNLEdBQ3REO1FBQUVZLFdBQVc7UUFBS0MsU0FBUztJQUFHO0lBR2hDLE1BQU1JLGVBQWV2Qix3REFBU0EsQ0FDNUJELDJEQUFZQSxDQUFDZSxpQkFBaUI7UUFBQztRQUFHO1FBQUs7UUFBSztLQUFFLEVBQUU7UUFBQztRQUFHO1FBQUc7UUFBRztLQUFFLEdBQzVEO1FBQUVJLFdBQVc7UUFBS0MsU0FBUztJQUFHO0lBR2hDLHVCQUF1QjtJQUN2QnhCLGdEQUFTQTtxQ0FBQztZQUNSLElBQUljLFNBQVMsVUFBVUksSUFBSVcsT0FBTyxFQUFFO2dCQUNsQyxNQUFNQyxVQUFVWixJQUFJVyxPQUFPO2dCQUUzQnZCLHNDQUFJQSxDQUFDeUIsTUFBTSxDQUFDRCxTQUNWO29CQUNFUixHQUFHVCxjQUFjLE9BQU8sTUFBTUYsUUFBUSxDQUFDLE1BQU1BO29CQUM3Q0ksT0FBT0EsUUFBUSxNQUFNO29CQUNyQmlCLFVBQVVoQixTQUFTLENBQUMsS0FBSztvQkFDekJDLFNBQVNBLFVBQVUsSUFBSTtnQkFDekIsR0FDQTtvQkFDRUssR0FBR1QsY0FBYyxPQUFPLENBQUMsTUFBTUYsUUFBUSxNQUFNQTtvQkFDN0NJLE9BQU9BLFFBQVEsTUFBTTtvQkFDckJpQixVQUFVaEIsU0FBUyxLQUFLO29CQUN4QkMsU0FBU0EsVUFBVSxJQUFJO29CQUN2QmdCLE1BQU07b0JBQ05DLGVBQWU7d0JBQ2JDLFNBQVNMO3dCQUNUTSxPQUFPO3dCQUNQQyxLQUFLO3dCQUNMQyxPQUFPO3dCQUNQQyxxQkFBcUI7b0JBQ3ZCO2dCQUNGO2dCQUdGO2lEQUFPO3dCQUNMaEMsNkRBQWFBLENBQUNpQyxNQUFNLEdBQUdDLE9BQU87eURBQUNOLENBQUFBLFVBQVdBLFFBQVFPLElBQUk7O29CQUN4RDs7WUFDRjtRQUNGO29DQUFHO1FBQUM1QjtRQUFNSDtRQUFPRTtRQUFXRTtRQUFPQztRQUFRQztLQUFRO0lBRW5ELElBQUlILFNBQVMsUUFBUTtRQUNuQixxQkFDRSw4REFBQzZCO1lBQUl6QixLQUFLQTtZQUFLTixXQUFXQTtzQkFDdkJGOzs7Ozs7SUFHUDtJQUVBLHFCQUNFLDhEQUFDUixpREFBTUEsQ0FBQ3lDLEdBQUc7UUFDVHpCLEtBQUtBO1FBQ0wwQixPQUFPO1lBQ0x0QjtZQUNBUCxPQUFPQSxRQUFRVyxhQUFhO1lBQzVCVixRQUFRQSxTQUFTVyxjQUFjO1lBQy9CVixTQUFTQSxVQUFVVyxlQUFlO1FBQ3BDO1FBQ0FoQixXQUFXQTtrQkFFVkY7Ozs7OztBQUdQO0dBakd3QkQ7O1FBV01OLG9EQUFTQTtRQU0zQkUsb0RBQVNBO1FBU0FBLG9EQUFTQTtRQUtSQSxvREFBU0E7UUFLUkEsb0RBQVNBOzs7S0FwQ1JJO0FBbUd4QixrQ0FBa0M7QUFDM0IsU0FBU29DLGNBQWMsS0FnQjdCO1FBaEI2QixFQUM1QkMsR0FBRyxFQUNIQyxHQUFHLEVBQ0huQyxZQUFZLEVBQUUsRUFDZEQsUUFBUSxHQUFHLEVBQ1hHLE9BQU8sUUFBUSxFQUNmQyxRQUFRLEtBQUssRUFDYkMsU0FBUyxLQUFLLEVBU2YsR0FoQjZCO0lBaUI1QixxQkFDRSw4REFBQ1A7UUFBZ0JFLE9BQU9BO1FBQU9DLFdBQVdBO1FBQVdFLE1BQU1BO1FBQU1DLE9BQU9BO1FBQU9DLFFBQVFBO2tCQUNyRiw0RUFBQ2dDO1lBQUlGLEtBQUtBO1lBQUtDLEtBQUtBO1lBQUtuQyxXQUFVOzs7Ozs7Ozs7OztBQUd6QztNQXRCZ0JpQztBQXdCVCxTQUFTSSxhQUFhLEtBWTVCO1FBWjRCLEVBQzNCdkMsUUFBUSxFQUNSRSxZQUFZLEVBQUUsRUFDZEQsUUFBUSxHQUFHLEVBQ1hHLE9BQU8sUUFBUSxFQUNmRyxVQUFVLEtBQUssRUFPaEIsR0FaNEI7SUFhM0IscUJBQ0UsOERBQUNSO1FBQWdCRSxPQUFPQTtRQUFPQyxXQUFXQTtRQUFXRSxNQUFNQTtRQUFNRyxTQUFTQTtrQkFDdkVQOzs7Ozs7QUFHUDtNQWxCZ0J1QztBQW9CaEIsK0JBQStCO0FBQ3hCLFNBQVNDLGFBQWEsS0FVNUI7UUFWNEIsRUFDM0J4QyxRQUFRLEVBQ1JFLFlBQVksRUFBRSxFQUNkRCxRQUFRLEdBQUcsRUFDWEcsT0FBTyxRQUFRLEVBTWhCLEdBVjRCO0lBVzNCLHFCQUNFLDhEQUFDTDtRQUNDRSxPQUFPQTtRQUNQQyxXQUFXQTtRQUNYRSxNQUFNQTtRQUNOQyxPQUFPO1FBQ1BFLFNBQVM7a0JBRVJQOzs7Ozs7QUFHUDtNQXRCZ0J3QztBQXdCVCxTQUFTQyxtQkFBbUIsS0FVbEM7UUFWa0MsRUFDakN6QyxRQUFRLEVBQ1JFLFlBQVksRUFBRSxFQUNkRCxRQUFRLEdBQUcsRUFDWEUsWUFBWSxJQUFhLEVBTTFCLEdBVmtDO0lBV2pDLHFCQUNFLDhEQUFDSjtRQUNDRSxPQUFPQTtRQUNQQyxXQUFXQTtRQUNYQyxXQUFXQTtRQUNYQyxNQUFLO2tCQUVKSjs7Ozs7O0FBR1A7TUFyQmdCeUM7QUF1QmhCLCtCQUErQjtBQUN4QixTQUFTQyxnQkFBZ0IsS0FRL0I7UUFSK0IsRUFDOUIxQyxRQUFRLEVBQ1JFLFlBQVksRUFBRSxFQUNkeUMsWUFBWSxDQUFDLEVBS2QsR0FSK0I7O0lBUzlCLE1BQU1uQyxNQUFNakIsNkNBQU1BLENBQWlCO0lBRW5DRCxnREFBU0E7cUNBQUM7WUFDUixJQUFJa0IsSUFBSVcsT0FBTyxFQUFFO2dCQUNmdkIsc0NBQUlBLENBQUNnRCxFQUFFLENBQUNwQyxJQUFJVyxPQUFPLEVBQUU7b0JBQ25CUCxHQUFHLEtBQW9CLE9BQWYsS0FBSytCO29CQUNiRSxVQUFVO29CQUNWdEIsTUFBTTtvQkFDTnVCLE1BQU07b0JBQ05DLFFBQVEsQ0FBQztnQkFDWDtZQUNGO1FBQ0Y7b0NBQUc7UUFBQ0o7S0FBVTtJQUVkLHFCQUNFLDhEQUFDVjtRQUFJekIsS0FBS0E7UUFBS04sV0FBV0E7a0JBQ3ZCRjs7Ozs7O0FBR1A7SUE1QmdCMEM7TUFBQUE7QUE4QmhCLHdCQUF3QjtBQUNqQixTQUFTTSxnQkFBZ0IsS0FRL0I7UUFSK0IsRUFDOUJoRCxRQUFRLEVBQ1JFLFlBQVksRUFBRSxFQUNkK0MsV0FBVyxHQUFHLEVBS2YsR0FSK0I7O0lBUzlCLE1BQU16QyxNQUFNakIsNkNBQU1BLENBQWlCO0lBRW5DRCxnREFBU0E7cUNBQUM7WUFDUixNQUFNOEIsVUFBVVosSUFBSVcsT0FBTztZQUMzQixJQUFJLENBQUNDLFNBQVM7WUFFZCxNQUFNOEI7NkRBQWtCLENBQUNDO29CQUN2QixNQUFNQyxPQUFPaEMsUUFBUWlDLHFCQUFxQjtvQkFDMUMsTUFBTUMsSUFBSUgsRUFBRUksT0FBTyxHQUFHSCxLQUFLSSxJQUFJLEdBQUdKLEtBQUtLLEtBQUssR0FBRztvQkFDL0MsTUFBTTdDLElBQUl1QyxFQUFFTyxPQUFPLEdBQUdOLEtBQUtPLEdBQUcsR0FBR1AsS0FBS1EsTUFBTSxHQUFHO29CQUUvQ2hFLHNDQUFJQSxDQUFDZ0QsRUFBRSxDQUFDeEIsU0FBUzt3QkFDZmtDLEdBQUdBLElBQUlMO3dCQUNQckMsR0FBR0EsSUFBSXFDO3dCQUNQSixVQUFVO3dCQUNWdEIsTUFBTTtvQkFDUjtnQkFDRjs7WUFFQSxNQUFNc0M7OERBQW1CO29CQUN2QmpFLHNDQUFJQSxDQUFDZ0QsRUFBRSxDQUFDeEIsU0FBUzt3QkFDZmtDLEdBQUc7d0JBQ0gxQyxHQUFHO3dCQUNIaUMsVUFBVTt3QkFDVnRCLE1BQU07b0JBQ1I7Z0JBQ0Y7O1lBRUFILFFBQVEwQyxnQkFBZ0IsQ0FBQyxhQUFhWjtZQUN0QzlCLFFBQVEwQyxnQkFBZ0IsQ0FBQyxjQUFjRDtZQUV2Qzs2Q0FBTztvQkFDTHpDLFFBQVEyQyxtQkFBbUIsQ0FBQyxhQUFhYjtvQkFDekM5QixRQUFRMkMsbUJBQW1CLENBQUMsY0FBY0Y7Z0JBQzVDOztRQUNGO29DQUFHO1FBQUNaO0tBQVM7SUFFYixxQkFDRSw4REFBQ2hCO1FBQUl6QixLQUFLQTtRQUFLTixXQUFXQTtrQkFDdkJGOzs7Ozs7QUFHUDtJQW5EZ0JnRDtNQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhcmtpdFxcRGVza3RvcFxcY29va2llc1xccG9ydGZvbGlvXFxzcmNcXGNvbXBvbmVudHNcXGFuaW1hdGlvbnNcXFBhcmFsbGF4RWxlbWVudC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uLCB1c2VTY3JvbGwsIHVzZVRyYW5zZm9ybSwgdXNlU3ByaW5nIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBnc2FwIH0gZnJvbSAnZ3NhcCc7XG5pbXBvcnQgeyBTY3JvbGxUcmlnZ2VyIH0gZnJvbSAnZ3NhcC9TY3JvbGxUcmlnZ2VyJztcblxuLy8gUmVnaXN0ZXIgR1NBUCBwbHVnaW5zXG5pZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgZ3NhcC5yZWdpc3RlclBsdWdpbihTY3JvbGxUcmlnZ2VyKTtcbn1cblxuaW50ZXJmYWNlIFBhcmFsbGF4RWxlbWVudFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgc3BlZWQ/OiBudW1iZXI7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgZGlyZWN0aW9uPzogJ3VwJyB8ICdkb3duJztcbiAgdHlwZT86ICdmcmFtZXInIHwgJ2dzYXAnO1xuICBzY2FsZT86IGJvb2xlYW47XG4gIHJvdGF0ZT86IGJvb2xlYW47XG4gIG9wYWNpdHk/OiBib29sZWFuO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQYXJhbGxheEVsZW1lbnQoe1xuICBjaGlsZHJlbixcbiAgc3BlZWQgPSAwLjUsXG4gIGNsYXNzTmFtZSA9ICcnLFxuICBkaXJlY3Rpb24gPSAndXAnLFxuICB0eXBlID0gJ2ZyYW1lcicsXG4gIHNjYWxlID0gZmFsc2UsXG4gIHJvdGF0ZSA9IGZhbHNlLFxuICBvcGFjaXR5ID0gZmFsc2Vcbn06IFBhcmFsbGF4RWxlbWVudFByb3BzKSB7XG4gIGNvbnN0IHJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IHsgc2Nyb2xsWVByb2dyZXNzIH0gPSB1c2VTY3JvbGwoe1xuICAgIHRhcmdldDogcmVmLFxuICAgIG9mZnNldDogW1wic3RhcnQgZW5kXCIsIFwiZW5kIHN0YXJ0XCJdXG4gIH0pO1xuXG4gIC8vIEZyYW1lciBNb3Rpb24gdHJhbnNmb3JtcyB3aXRoIHNwcmluZyBwaHlzaWNzXG4gIGNvbnN0IHkgPSB1c2VTcHJpbmcoXG4gICAgdXNlVHJhbnNmb3JtKFxuICAgICAgc2Nyb2xsWVByb2dyZXNzLFxuICAgICAgWzAsIDFdLFxuICAgICAgZGlyZWN0aW9uID09PSAndXAnID8gWzAsIC1zcGVlZCAqIDEwMF0gOiBbMCwgc3BlZWQgKiAxMDBdXG4gICAgKSxcbiAgICB7IHN0aWZmbmVzczogMTAwLCBkYW1waW5nOiAzMCwgcmVzdERlbHRhOiAwLjAwMSB9XG4gICk7XG5cbiAgY29uc3Qgc2NhbGVWYWx1ZSA9IHVzZVNwcmluZyhcbiAgICB1c2VUcmFuc2Zvcm0oc2Nyb2xsWVByb2dyZXNzLCBbMCwgMC41LCAxXSwgWzAuOCwgMS4xLCAwLjhdKSxcbiAgICB7IHN0aWZmbmVzczogMTAwLCBkYW1waW5nOiAzMCB9XG4gICk7XG5cbiAgY29uc3Qgcm90YXRlVmFsdWUgPSB1c2VTcHJpbmcoXG4gICAgdXNlVHJhbnNmb3JtKHNjcm9sbFlQcm9ncmVzcywgWzAsIDFdLCBbMCwgMzYwICogc3BlZWRdKSxcbiAgICB7IHN0aWZmbmVzczogMTAwLCBkYW1waW5nOiAzMCB9XG4gICk7XG5cbiAgY29uc3Qgb3BhY2l0eVZhbHVlID0gdXNlU3ByaW5nKFxuICAgIHVzZVRyYW5zZm9ybShzY3JvbGxZUHJvZ3Jlc3MsIFswLCAwLjIsIDAuOCwgMV0sIFswLCAxLCAxLCAwXSksXG4gICAgeyBzdGlmZm5lc3M6IDEwMCwgZGFtcGluZzogMzAgfVxuICApO1xuXG4gIC8vIEdTQVAgcGFyYWxsYXggZWZmZWN0XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHR5cGUgPT09ICdnc2FwJyAmJiByZWYuY3VycmVudCkge1xuICAgICAgY29uc3QgZWxlbWVudCA9IHJlZi5jdXJyZW50O1xuXG4gICAgICBnc2FwLmZyb21UbyhlbGVtZW50LFxuICAgICAgICB7XG4gICAgICAgICAgeTogZGlyZWN0aW9uID09PSAndXAnID8gMTAwICogc3BlZWQgOiAtMTAwICogc3BlZWQsXG4gICAgICAgICAgc2NhbGU6IHNjYWxlID8gMC44IDogMSxcbiAgICAgICAgICByb3RhdGlvbjogcm90YXRlID8gLTEwIDogMCxcbiAgICAgICAgICBvcGFjaXR5OiBvcGFjaXR5ID8gMCA6IDEsXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICB5OiBkaXJlY3Rpb24gPT09ICd1cCcgPyAtMTAwICogc3BlZWQgOiAxMDAgKiBzcGVlZCxcbiAgICAgICAgICBzY2FsZTogc2NhbGUgPyAxLjEgOiAxLFxuICAgICAgICAgIHJvdGF0aW9uOiByb3RhdGUgPyAxMCA6IDAsXG4gICAgICAgICAgb3BhY2l0eTogb3BhY2l0eSA/IDEgOiAxLFxuICAgICAgICAgIGVhc2U6IFwibm9uZVwiLFxuICAgICAgICAgIHNjcm9sbFRyaWdnZXI6IHtcbiAgICAgICAgICAgIHRyaWdnZXI6IGVsZW1lbnQsXG4gICAgICAgICAgICBzdGFydDogXCJ0b3AgYm90dG9tXCIsXG4gICAgICAgICAgICBlbmQ6IFwiYm90dG9tIHRvcFwiLFxuICAgICAgICAgICAgc2NydWI6IDEsXG4gICAgICAgICAgICBpbnZhbGlkYXRlT25SZWZyZXNoOiB0cnVlLFxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKTtcblxuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgU2Nyb2xsVHJpZ2dlci5nZXRBbGwoKS5mb3JFYWNoKHRyaWdnZXIgPT4gdHJpZ2dlci5raWxsKCkpO1xuICAgICAgfTtcbiAgICB9XG4gIH0sIFt0eXBlLCBzcGVlZCwgZGlyZWN0aW9uLCBzY2FsZSwgcm90YXRlLCBvcGFjaXR5XSk7XG5cbiAgaWYgKHR5cGUgPT09ICdnc2FwJykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHJlZj17cmVmfSBjbGFzc05hbWU9e2NsYXNzTmFtZX0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxtb3Rpb24uZGl2XG4gICAgICByZWY9e3JlZn1cbiAgICAgIHN0eWxlPXt7XG4gICAgICAgIHksXG4gICAgICAgIHNjYWxlOiBzY2FsZSA/IHNjYWxlVmFsdWUgOiAxLFxuICAgICAgICByb3RhdGU6IHJvdGF0ZSA/IHJvdGF0ZVZhbHVlIDogMCxcbiAgICAgICAgb3BhY2l0eTogb3BhY2l0eSA/IG9wYWNpdHlWYWx1ZSA6IDFcbiAgICAgIH19XG4gICAgICBjbGFzc05hbWU9e2NsYXNzTmFtZX1cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9tb3Rpb24uZGl2PlxuICApO1xufVxuXG4vLyBTcGVjaWFsaXplZCBwYXJhbGxheCBjb21wb25lbnRzXG5leHBvcnQgZnVuY3Rpb24gUGFyYWxsYXhJbWFnZSh7XG4gIHNyYyxcbiAgYWx0LFxuICBjbGFzc05hbWUgPSAnJyxcbiAgc3BlZWQgPSAwLjUsXG4gIHR5cGUgPSAnZnJhbWVyJyxcbiAgc2NhbGUgPSBmYWxzZSxcbiAgcm90YXRlID0gZmFsc2Vcbn06IHtcbiAgc3JjOiBzdHJpbmc7XG4gIGFsdDogc3RyaW5nO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIHNwZWVkPzogbnVtYmVyO1xuICB0eXBlPzogJ2ZyYW1lcicgfCAnZ3NhcCc7XG4gIHNjYWxlPzogYm9vbGVhbjtcbiAgcm90YXRlPzogYm9vbGVhbjtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8UGFyYWxsYXhFbGVtZW50IHNwZWVkPXtzcGVlZH0gY2xhc3NOYW1lPXtjbGFzc05hbWV9IHR5cGU9e3R5cGV9IHNjYWxlPXtzY2FsZX0gcm90YXRlPXtyb3RhdGV9PlxuICAgICAgPGltZyBzcmM9e3NyY30gYWx0PXthbHR9IGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCIgLz5cbiAgICA8L1BhcmFsbGF4RWxlbWVudD5cbiAgKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFBhcmFsbGF4VGV4dCh7XG4gIGNoaWxkcmVuLFxuICBjbGFzc05hbWUgPSAnJyxcbiAgc3BlZWQgPSAwLjMsXG4gIHR5cGUgPSAnZnJhbWVyJyxcbiAgb3BhY2l0eSA9IGZhbHNlXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgc3BlZWQ/OiBudW1iZXI7XG4gIHR5cGU/OiAnZnJhbWVyJyB8ICdnc2FwJztcbiAgb3BhY2l0eT86IGJvb2xlYW47XG59KSB7XG4gIHJldHVybiAoXG4gICAgPFBhcmFsbGF4RWxlbWVudCBzcGVlZD17c3BlZWR9IGNsYXNzTmFtZT17Y2xhc3NOYW1lfSB0eXBlPXt0eXBlfSBvcGFjaXR5PXtvcGFjaXR5fT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1BhcmFsbGF4RWxlbWVudD5cbiAgKTtcbn1cblxuLy8gQWR2YW5jZWQgcGFyYWxsYXggY29tcG9uZW50c1xuZXhwb3J0IGZ1bmN0aW9uIFBhcmFsbGF4Q2FyZCh7XG4gIGNoaWxkcmVuLFxuICBjbGFzc05hbWUgPSAnJyxcbiAgc3BlZWQgPSAwLjQsXG4gIHR5cGUgPSAnZnJhbWVyJ1xufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIHNwZWVkPzogbnVtYmVyO1xuICB0eXBlPzogJ2ZyYW1lcicgfCAnZ3NhcCc7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPFBhcmFsbGF4RWxlbWVudFxuICAgICAgc3BlZWQ9e3NwZWVkfVxuICAgICAgY2xhc3NOYW1lPXtjbGFzc05hbWV9XG4gICAgICB0eXBlPXt0eXBlfVxuICAgICAgc2NhbGU9e3RydWV9XG4gICAgICBvcGFjaXR5PXt0cnVlfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1BhcmFsbGF4RWxlbWVudD5cbiAgKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFBhcmFsbGF4QmFja2dyb3VuZCh7XG4gIGNoaWxkcmVuLFxuICBjbGFzc05hbWUgPSAnJyxcbiAgc3BlZWQgPSAwLjgsXG4gIGRpcmVjdGlvbiA9ICd1cCcgYXMgY29uc3Rcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBzcGVlZD86IG51bWJlcjtcbiAgZGlyZWN0aW9uPzogJ3VwJyB8ICdkb3duJztcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8UGFyYWxsYXhFbGVtZW50XG4gICAgICBzcGVlZD17c3BlZWR9XG4gICAgICBjbGFzc05hbWU9e2NsYXNzTmFtZX1cbiAgICAgIGRpcmVjdGlvbj17ZGlyZWN0aW9ufVxuICAgICAgdHlwZT1cImdzYXBcIlxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1BhcmFsbGF4RWxlbWVudD5cbiAgKTtcbn1cblxuLy8gRmxvYXRpbmcgYW5pbWF0aW9uIGNvbXBvbmVudFxuZXhwb3J0IGZ1bmN0aW9uIEZsb2F0aW5nRWxlbWVudCh7XG4gIGNoaWxkcmVuLFxuICBjbGFzc05hbWUgPSAnJyxcbiAgaW50ZW5zaXR5ID0gMVxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIGludGVuc2l0eT86IG51bWJlcjtcbn0pIHtcbiAgY29uc3QgcmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChyZWYuY3VycmVudCkge1xuICAgICAgZ3NhcC50byhyZWYuY3VycmVudCwge1xuICAgICAgICB5OiBgKz0kezIwICogaW50ZW5zaXR5fWAsXG4gICAgICAgIGR1cmF0aW9uOiAzLFxuICAgICAgICBlYXNlOiBcInBvd2VyMi5pbk91dFwiLFxuICAgICAgICB5b3lvOiB0cnVlLFxuICAgICAgICByZXBlYXQ6IC0xXG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtpbnRlbnNpdHldKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuLy8gTWFnbmV0aWMgaG92ZXIgZWZmZWN0XG5leHBvcnQgZnVuY3Rpb24gTWFnbmV0aWNFbGVtZW50KHtcbiAgY2hpbGRyZW4sXG4gIGNsYXNzTmFtZSA9ICcnLFxuICBzdHJlbmd0aCA9IDAuM1xufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIHN0cmVuZ3RoPzogbnVtYmVyO1xufSkge1xuICBjb25zdCByZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgZWxlbWVudCA9IHJlZi5jdXJyZW50O1xuICAgIGlmICghZWxlbWVudCkgcmV0dXJuO1xuXG4gICAgY29uc3QgaGFuZGxlTW91c2VNb3ZlID0gKGU6IE1vdXNlRXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHJlY3QgPSBlbGVtZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgICAgY29uc3QgeCA9IGUuY2xpZW50WCAtIHJlY3QubGVmdCAtIHJlY3Qud2lkdGggLyAyO1xuICAgICAgY29uc3QgeSA9IGUuY2xpZW50WSAtIHJlY3QudG9wIC0gcmVjdC5oZWlnaHQgLyAyO1xuXG4gICAgICBnc2FwLnRvKGVsZW1lbnQsIHtcbiAgICAgICAgeDogeCAqIHN0cmVuZ3RoLFxuICAgICAgICB5OiB5ICogc3RyZW5ndGgsXG4gICAgICAgIGR1cmF0aW9uOiAwLjMsXG4gICAgICAgIGVhc2U6IFwicG93ZXIyLm91dFwiXG4gICAgICB9KTtcbiAgICB9O1xuXG4gICAgY29uc3QgaGFuZGxlTW91c2VMZWF2ZSA9ICgpID0+IHtcbiAgICAgIGdzYXAudG8oZWxlbWVudCwge1xuICAgICAgICB4OiAwLFxuICAgICAgICB5OiAwLFxuICAgICAgICBkdXJhdGlvbjogMC41LFxuICAgICAgICBlYXNlOiBcImVsYXN0aWMub3V0KDEsIDAuMylcIlxuICAgICAgfSk7XG4gICAgfTtcblxuICAgIGVsZW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgaGFuZGxlTW91c2VNb3ZlKTtcbiAgICBlbGVtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbGVhdmUnLCBoYW5kbGVNb3VzZUxlYXZlKTtcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBlbGVtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIGhhbmRsZU1vdXNlTW92ZSk7XG4gICAgICBlbGVtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbGVhdmUnLCBoYW5kbGVNb3VzZUxlYXZlKTtcbiAgICB9O1xuICB9LCBbc3RyZW5ndGhdKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJtb3Rpb24iLCJ1c2VTY3JvbGwiLCJ1c2VUcmFuc2Zvcm0iLCJ1c2VTcHJpbmciLCJnc2FwIiwiU2Nyb2xsVHJpZ2dlciIsInJlZ2lzdGVyUGx1Z2luIiwiUGFyYWxsYXhFbGVtZW50IiwiY2hpbGRyZW4iLCJzcGVlZCIsImNsYXNzTmFtZSIsImRpcmVjdGlvbiIsInR5cGUiLCJzY2FsZSIsInJvdGF0ZSIsIm9wYWNpdHkiLCJyZWYiLCJzY3JvbGxZUHJvZ3Jlc3MiLCJ0YXJnZXQiLCJvZmZzZXQiLCJ5Iiwic3RpZmZuZXNzIiwiZGFtcGluZyIsInJlc3REZWx0YSIsInNjYWxlVmFsdWUiLCJyb3RhdGVWYWx1ZSIsIm9wYWNpdHlWYWx1ZSIsImN1cnJlbnQiLCJlbGVtZW50IiwiZnJvbVRvIiwicm90YXRpb24iLCJlYXNlIiwic2Nyb2xsVHJpZ2dlciIsInRyaWdnZXIiLCJzdGFydCIsImVuZCIsInNjcnViIiwiaW52YWxpZGF0ZU9uUmVmcmVzaCIsImdldEFsbCIsImZvckVhY2giLCJraWxsIiwiZGl2Iiwic3R5bGUiLCJQYXJhbGxheEltYWdlIiwic3JjIiwiYWx0IiwiaW1nIiwiUGFyYWxsYXhUZXh0IiwiUGFyYWxsYXhDYXJkIiwiUGFyYWxsYXhCYWNrZ3JvdW5kIiwiRmxvYXRpbmdFbGVtZW50IiwiaW50ZW5zaXR5IiwidG8iLCJkdXJhdGlvbiIsInlveW8iLCJyZXBlYXQiLCJNYWduZXRpY0VsZW1lbnQiLCJzdHJlbmd0aCIsImhhbmRsZU1vdXNlTW92ZSIsImUiLCJyZWN0IiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0IiwieCIsImNsaWVudFgiLCJsZWZ0Iiwid2lkdGgiLCJjbGllbnRZIiwidG9wIiwiaGVpZ2h0IiwiaGFuZGxlTW91c2VMZWF2ZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/animations/ParallaxElement.tsx\n"));

/***/ })

});