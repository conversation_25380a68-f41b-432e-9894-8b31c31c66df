export default function ExperienceSection() {
  return (
    <section id="experience" className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="bg-deep-charcoal dark:bg-dark-surface rounded-2xl p-8 lg:p-12">
        <h2 className="text-3xl lg:text-4xl font-bold text-light-almond dark:text-dark-text mb-12">
          Cool projects I've been a part of
        </h2>

        <div className="space-y-8">
          {/* Zero */}
          <div className="flex items-center justify-between py-4 border-b border-light-almond/10 dark:border-dark-text/10">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-light-almond dark:bg-dark-text rounded-lg flex items-center justify-center">
                <div className="w-6 h-6 bg-deep-charcoal dark:bg-dark-bg rounded-sm flex items-center justify-center">
                  <div className="w-3 h-3 border-2 border-light-almond dark:border-dark-text rounded-sm"></div>
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-light-almond dark:text-dark-text">Zero</h3>
                <p className="text-sm text-light-almond/70 dark:text-dark-text/70">ICT | Software Engineer</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-light-almond/70 dark:text-dark-text/70">June 2025 - Present</p>
            </div>
          </div>

          {/* Google Summer of Code */}
          <div className="flex items-center justify-between py-4 border-b border-light-almond/10 dark:border-dark-text/10">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">G</span>
              </div>
              <div>
                <h3 className="font-semibold text-light-almond dark:text-dark-text">Google Summer of Code</h3>
                <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Contributor under Deepmind</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-light-almond/70 dark:text-dark-text/70">May 2025 - July 2025</p>
            </div>
          </div>

          {/* Cal.com */}
          <div className="flex items-center justify-between py-4 border-b border-light-almond/10 dark:border-dark-text/10">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">cal</span>
              </div>
              <div>
                <h3 className="font-semibold text-light-almond dark:text-dark-text">Cal.com</h3>
                <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Software Engineer Intern</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-light-almond/70 dark:text-dark-text/70">February 2025 - May 2025</p>
            </div>
          </div>

          {/* Superteam */}
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-light-almond dark:text-dark-text">Superteam, Solana Foundation</h3>
                <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Member | Grant Recipient</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-light-almond/70 dark:text-dark-text/70">November 2024 - Present</p>
            </div>
          </div>
        </div>

        {/* Education Section */}
        <div className="mt-16">
          <h2 className="text-2xl lg:text-3xl font-bold text-light-almond dark:text-dark-text mb-8">
            Education
          </h2>

          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6L21 9l-9-6zM18.82 9L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z"/>
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-light-almond dark:text-dark-text">Mumbai University</h3>
                <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Information and technology</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-light-almond/70 dark:text-dark-text/70">2021 - 2024 </p>
            </div>
          </div>
        </div>
      </div>
      </div>
    </section>
  );
}
