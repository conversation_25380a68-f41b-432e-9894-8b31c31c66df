"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ContributionSection.tsx":
/*!*********************************************************!*\
  !*** ./src/components/sections/ContributionSection.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContributionSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink!=!lucide-react */ \"(app-pages-browser)/../../../node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ContributionSection() {\n    _s();\n    const username = 'Arkit-k';\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('2025');\n    const years = [\n        '2025',\n        '2024',\n        '2023',\n        '2022',\n        '2021'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"px-6 lg:px-12 mt-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900 rounded-2xl p-8 border border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Contribution Graph\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"167 contributions in the last year\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Less\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                        lineNumber: 25,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 bg-gray-700 rounded-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                lineNumber: 27,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 bg-green-900 rounded-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                lineNumber: 28,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 bg-green-700 rounded-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                lineNumber: 29,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 bg-green-500 rounded-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                lineNumber: 30,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 bg-green-400 rounded-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                lineNumber: 31,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                        lineNumber: 26,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"More\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                        lineNumber: 33,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: years.map((year)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedYear(year),\n                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(selectedYear === year ? 'bg-green-600 text-white' : 'bg-gray-800 text-gray-400 hover:bg-gray-700 hover:text-white'),\n                                        children: year\n                                    }, year, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 rounded-xl p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-xs text-gray-400 mb-3 px-8\",\n                                children: [\n                                    'Jul',\n                                    'Aug',\n                                    'Sep',\n                                    'Oct',\n                                    'Nov',\n                                    'Dec',\n                                    'Jan',\n                                    'Feb',\n                                    'Mar',\n                                    'Apr',\n                                    'May',\n                                    'Jun'\n                                ].map((month)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: month\n                                    }, month, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col justify-between text-xs text-gray-400 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Mon\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Wed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Fri\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-53 gap-1\",\n                                            children: Array.from({\n                                                length: 371\n                                            }, (_, i)=>{\n                                                // Generate random contribution levels for demo\n                                                const level = Math.floor(Math.random() * 5);\n                                                const colors = [\n                                                    'bg-gray-700',\n                                                    'bg-green-900',\n                                                    'bg-green-700',\n                                                    'bg-green-500',\n                                                    'bg-green-400'\n                                                ];\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-sm \".concat(colors[level], \" hover:ring-1 hover:ring-white/50 transition-all cursor-pointer\"),\n                                                    title: \"\".concat(level, \" contributions\")\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Learn how we count contributions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://github.com/\".concat(username),\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"View on GitHub\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_s(ContributionSection, \"5KLrMHf7GPajiuU0tNK+VnS0Q8k=\");\n_c = ContributionSection;\nvar _c;\n$RefreshReg$(_c, \"ContributionSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ContributionSection.tsx\n"));

/***/ })

});