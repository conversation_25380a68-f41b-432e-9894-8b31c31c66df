"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-spring.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSpring: () => (/* binding */ useSpring)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/spring-value.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var _use_transform_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n\n\n\n\n\n\nfunction useSpring(source, options = {}) {\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionConfigContext);\n    const getFromSource = () => ((0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.isMotionValue)(source) ? source.get() : source);\n    // isStatic will never change, allowing early hooks return\n    if (isStatic) {\n        return (0,_use_transform_mjs__WEBPACK_IMPORTED_MODULE_3__.useTransform)(getFromSource);\n    }\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__.useMotionValue)(getFromSource());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => {\n        return (0,motion_dom__WEBPACK_IMPORTED_MODULE_5__.attachSpring)(value, source, options);\n    }, [value, JSON.stringify(options)]);\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLXNwcmluZy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF5RDtBQUNGO0FBQ2tCO0FBQ2pCO0FBQ0w7O0FBRW5ELHVDQUF1QztBQUN2QyxZQUFZLFdBQVcsRUFBRSxpREFBVSxDQUFDLGlGQUFtQjtBQUN2RCxpQ0FBaUMseURBQWE7QUFDOUM7QUFDQTtBQUNBLGVBQWUsZ0VBQVk7QUFDM0I7QUFDQSxrQkFBa0IscUVBQWM7QUFDaEMsSUFBSSx5REFBa0I7QUFDdEIsZUFBZSx3REFBWTtBQUMzQixLQUFLO0FBQ0w7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYXJraXRcXERlc2t0b3BcXGNvb2tpZXNcXHBvcnRmb2xpb1xcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcdmFsdWVcXHVzZS1zcHJpbmcubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGF0dGFjaFNwcmluZywgaXNNb3Rpb25WYWx1ZSB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgdXNlQ29udGV4dCwgdXNlSW5zZXJ0aW9uRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgTW90aW9uQ29uZmlnQ29udGV4dCB9IGZyb20gJy4uL2NvbnRleHQvTW90aW9uQ29uZmlnQ29udGV4dC5tanMnO1xuaW1wb3J0IHsgdXNlTW90aW9uVmFsdWUgfSBmcm9tICcuL3VzZS1tb3Rpb24tdmFsdWUubWpzJztcbmltcG9ydCB7IHVzZVRyYW5zZm9ybSB9IGZyb20gJy4vdXNlLXRyYW5zZm9ybS5tanMnO1xuXG5mdW5jdGlvbiB1c2VTcHJpbmcoc291cmNlLCBvcHRpb25zID0ge30pIHtcbiAgICBjb25zdCB7IGlzU3RhdGljIH0gPSB1c2VDb250ZXh0KE1vdGlvbkNvbmZpZ0NvbnRleHQpO1xuICAgIGNvbnN0IGdldEZyb21Tb3VyY2UgPSAoKSA9PiAoaXNNb3Rpb25WYWx1ZShzb3VyY2UpID8gc291cmNlLmdldCgpIDogc291cmNlKTtcbiAgICAvLyBpc1N0YXRpYyB3aWxsIG5ldmVyIGNoYW5nZSwgYWxsb3dpbmcgZWFybHkgaG9va3MgcmV0dXJuXG4gICAgaWYgKGlzU3RhdGljKSB7XG4gICAgICAgIHJldHVybiB1c2VUcmFuc2Zvcm0oZ2V0RnJvbVNvdXJjZSk7XG4gICAgfVxuICAgIGNvbnN0IHZhbHVlID0gdXNlTW90aW9uVmFsdWUoZ2V0RnJvbVNvdXJjZSgpKTtcbiAgICB1c2VJbnNlcnRpb25FZmZlY3QoKCkgPT4ge1xuICAgICAgICByZXR1cm4gYXR0YWNoU3ByaW5nKHZhbHVlLCBzb3VyY2UsIG9wdGlvbnMpO1xuICAgIH0sIFt2YWx1ZSwgSlNPTi5zdHJpbmdpZnkob3B0aW9ucyldKTtcbiAgICByZXR1cm4gdmFsdWU7XG59XG5cbmV4cG9ydCB7IHVzZVNwcmluZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/value/spring-value.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/value/spring-value.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachSpring: () => (/* binding */ attachSpring),\n/* harmony export */   springValue: () => (/* binding */ springValue)\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var _animation_JSAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../animation/JSAnimation.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/JSAnimation.mjs\");\n/* harmony import */ var _utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n/**\n * Create a `MotionValue` that animates to its latest value using a spring.\n * Can either be a value or track another `MotionValue`.\n *\n * ```jsx\n * const x = motionValue(0)\n * const y = transformValue(() => x.get() * 2) // double x\n * ```\n *\n * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction springValue(source, options) {\n    const initialValue = (0,_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(source) ? source.get() : source;\n    const value = (0,_index_mjs__WEBPACK_IMPORTED_MODULE_1__.motionValue)(initialValue);\n    attachSpring(value, source, options);\n    return value;\n}\nfunction attachSpring(value, source, options) {\n    const initialValue = value.get();\n    let activeAnimation = null;\n    let latestValue = initialValue;\n    let latestSetter;\n    const unit = typeof initialValue === \"string\"\n        ? initialValue.replace(/[\\d.-]/g, \"\")\n        : undefined;\n    const stopAnimation = () => {\n        if (activeAnimation) {\n            activeAnimation.stop();\n            activeAnimation = null;\n        }\n    };\n    const startAnimation = () => {\n        stopAnimation();\n        activeAnimation = new _animation_JSAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__.JSAnimation({\n            keyframes: [asNumber(value.get()), asNumber(latestValue)],\n            velocity: value.getVelocity(),\n            type: \"spring\",\n            restDelta: 0.001,\n            restSpeed: 0.01,\n            ...options,\n            onUpdate: latestSetter,\n        });\n    };\n    value.attach((v, set) => {\n        latestValue = v;\n        latestSetter = (latest) => set(parseValue(latest, unit));\n        _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.postRender(startAnimation);\n        return value.get();\n    }, stopAnimation);\n    let unsubscribe = undefined;\n    if ((0,_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(source)) {\n        unsubscribe = source.on(\"change\", (v) => value.set(parseValue(v, unit)));\n        value.on(\"destroy\", unsubscribe);\n    }\n    return unsubscribe;\n}\nfunction parseValue(v, unit) {\n    return unit ? v + unit : v;\n}\nfunction asNumber(v) {\n    return typeof v === \"number\" ? v : parseFloat(v);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/value/spring-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/animations/ParallaxElement.tsx":
/*!*******************************************************!*\
  !*** ./src/components/animations/ParallaxElement.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParallaxImage: () => (/* binding */ ParallaxImage),\n/* harmony export */   ParallaxText: () => (/* binding */ ParallaxText),\n/* harmony export */   \"default\": () => (/* binding */ ParallaxElement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ default,ParallaxImage,ParallaxText auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Register GSAP plugins\nif (true) {\n    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\n}\nfunction ParallaxElement(param) {\n    let { children, speed = 0.5, className = '', direction = 'up', type = 'framer', scale = false, rotate = false, opacity = false } = param;\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useScroll)({\n        target: ref,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    // Framer Motion transforms with spring physics\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring)((0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], direction === 'up' ? [\n        0,\n        -speed * 100\n    ] : [\n        0,\n        speed * 100\n    ]), {\n        stiffness: 100,\n        damping: 30,\n        restDelta: 0.001\n    });\n    const scaleValue = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring)((0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useTransform)(scrollYProgress, [\n        0,\n        0.5,\n        1\n    ], [\n        0.8,\n        1.1,\n        0.8\n    ]), {\n        stiffness: 100,\n        damping: 30\n    });\n    const rotateValue = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring)((0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        360 * speed\n    ]), {\n        stiffness: 100,\n        damping: 30\n    });\n    const opacityValue = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring)((0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useTransform)(scrollYProgress, [\n        0,\n        0.2,\n        0.8,\n        1\n    ], [\n        0,\n        1,\n        1,\n        0\n    ]), {\n        stiffness: 100,\n        damping: 30\n    });\n    // GSAP parallax effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParallaxElement.useEffect\": ()=>{\n            if (type === 'gsap' && ref.current) {\n                const element = ref.current;\n                gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(element, {\n                    y: direction === 'up' ? 100 * speed : -100 * speed,\n                    scale: scale ? 0.8 : 1,\n                    rotation: rotate ? -10 : 0,\n                    opacity: opacity ? 0 : 1\n                }, {\n                    y: direction === 'up' ? -100 * speed : 100 * speed,\n                    scale: scale ? 1.1 : 1,\n                    rotation: rotate ? 10 : 0,\n                    opacity: opacity ? 1 : 1,\n                    ease: \"none\",\n                    scrollTrigger: {\n                        trigger: element,\n                        start: \"top bottom\",\n                        end: \"bottom top\",\n                        scrub: 1,\n                        invalidateOnRefresh: true\n                    }\n                });\n                return ({\n                    \"ParallaxElement.useEffect\": ()=>{\n                        gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger.getAll().forEach({\n                            \"ParallaxElement.useEffect\": (trigger)=>trigger.kill()\n                        }[\"ParallaxElement.useEffect\"]);\n                    }\n                })[\"ParallaxElement.useEffect\"];\n            }\n        }\n    }[\"ParallaxElement.useEffect\"], [\n        type,\n        speed,\n        direction,\n        scale,\n        rotate,\n        opacity\n    ]);\n    if (type === 'gsap') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: ref,\n            className: className,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        ref: ref,\n        style: {\n            y,\n            scale: scale ? scaleValue : 1,\n            rotate: rotate ? rotateValue : 0,\n            opacity: opacity ? opacityValue : 1\n        },\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(ParallaxElement, \"khp6KH5wiM7dfg8g1oeRgemi9S0=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring\n    ];\n});\n_c = ParallaxElement;\n// Specialized parallax components\nfunction ParallaxImage(param) {\n    let { src, alt, className = '', speed = 0.5 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParallaxElement, {\n        speed: speed,\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: src,\n            alt: alt,\n            className: \"w-full h-full object-cover\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n            lineNumber: 137,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ParallaxImage;\nfunction ParallaxText(param) {\n    let { children, className = '', speed = 0.3 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParallaxElement, {\n        speed: speed,\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\animations\\\\ParallaxElement.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ParallaxText;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ParallaxElement\");\n$RefreshReg$(_c1, \"ParallaxImage\");\n$RefreshReg$(_c2, \"ParallaxText\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/animations/ParallaxElement.tsx\n"));

/***/ })

});