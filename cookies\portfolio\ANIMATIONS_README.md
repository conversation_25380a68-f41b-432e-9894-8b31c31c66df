# Portfolio Animations Setup

## 🎨 Animation Features Added

### 1. **Framer Motion Components**
- `SlideUp` - Elements slide up with fade in
- `SlideLeft` - Elements slide from right to left
- `FadeIn` - Simple fade in animation
- `ScaleIn` - Elements scale up from smaller size
- `ParallaxElement` - Parallax scrolling effects

### 2. **GSAP Smooth Scrolling**
- Smooth scroll provider for the entire page
- Parallax effects for background elements
- Scroll-triggered animations

### 3. **Custom CSS Classes**
- `.fade-in` - For GSAP fade animations
- `.scale-in` - For GSAP scale animations
- `.parallax-slow` - Slow parallax movement
- `.parallax-fast` - Fast parallax movement

## 🚀 How to Use

### Basic Animation Components
```tsx
import { SlideUp, FadeIn, ScaleIn, ParallaxElement } from "@/components/animations";

// Slide up animation
<SlideUp delay={0.2}>
  <h1>Your content here</h1>
</SlideUp>

// Fade in with delay
<FadeIn delay={0.5}>
  <p>Your text here</p>
</FadeIn>

// Scale in animation
<ScaleIn>
  <div>Your content</div>
</ScaleIn>

// Parallax effect
<ParallaxElement speed={0.5}>
  <img src="/your-image.jpg" alt="Parallax image" />
</ParallaxElement>
```

### Smooth Scroll Provider
The entire app is wrapped with `SmoothScrollProvider` in `page.tsx` for smooth scrolling effects.

## 🎯 Current Implementation

### HeroSection
- Title and description: `SlideUp` animation
- Social links: `FadeIn` with delay
- Character image: `SlideLeft` with `ParallaxElement`

### HireMeSection
- Character image: `ScaleIn` animation
- Title: `SlideUp` animation
- Content: `FadeIn` with delay

## 🔧 Customization

### Animation Timing
You can customize animation timing by passing props:
```tsx
<SlideUp delay={0.3} duration={0.8}>
  <YourComponent />
</SlideUp>
```

### Parallax Speed
Adjust parallax speed (0.1 = slow, 1.0 = fast):
```tsx
<ParallaxElement speed={0.3}>
  <YourContent />
</ParallaxElement>
```

## 🎨 Adding More Animations

### To add animations to other sections:
1. Import animation components
2. Wrap your content with the desired animation
3. Add appropriate delays for staggered effects

Example:
```tsx
import { SlideUp, FadeIn } from "@/components/animations";

<SlideUp>
  <h2>Section Title</h2>
</SlideUp>
<FadeIn delay={0.2}>
  <p>Section content</p>
</FadeIn>
```

## 📱 Performance Notes
- Animations use `will-change` CSS property for better performance
- Framer Motion uses hardware acceleration
- GSAP is optimized for smooth 60fps animations
- All animations respect `prefers-reduced-motion` settings

## 🐛 Troubleshooting
- Make sure to save your logo as `arkit-logo.png` in the `public` folder
- If animations don't work, check browser console for errors
- Ensure all animation components are properly imported

## 🎉 Next Steps
- Add more parallax elements to background
- Implement scroll-triggered text animations
- Add hover animations to interactive elements
- Consider adding page transition animations
