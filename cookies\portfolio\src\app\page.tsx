import { ThemeToggle } from "@/components/theme-toggle";
import { Footer } from "@/components/footer";
import Image from "next/image";
import Link from "next/link";
import { SmoothScrollProvider } from "@/components/animations";
import {
  HeroSection,
  AboutSection,
  ExperienceSection,
  HireMeSection,
  ContributionSection
} from "@/components/sections";



export default function Home() {
  return (
    <SmoothScrollProvider>
      <div className="min-h-screen bg-light-almond dark:bg-dark-bg transition-colors">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-6 lg:px-12 max-w-7xl mx-auto">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-3 text-deep-charcoal dark:text-dark-text hover:opacity-80 transition-opacity">
          <div className="w-10 h-10 relative">
            <Image
              src="/arkit-logo.png"
              alt="Arkit_k Logo"
              fill
              className="object-contain rounded-full"
            />
          </div>
          <span className="text-xl font-bold">Arkit_k</span>
        </Link>

        {/* Navigation Links */}
        <div className="hidden md:flex items-center space-x-8 text-deep-charcoal dark:text-dark-text">
          <a href="#about" className="hover:text-accent-green transition-colors">
            About
          </a>
          <a href="#projects" className="hover:text-accent-green transition-colors">
            Projects
          </a>
          <a href="#blog" className="hover:text-accent-green transition-colors">
            Blog
          </a>
          <a href="#photos" className="hover:text-accent-green transition-colors">
            Photos
          </a>
        </div>

        {/* Theme Toggle */}
        <ThemeToggle />
      </nav>



      {/* Main Content */}
      <main className="overflow-x-hidden">
        <HeroSection />
        <AboutSection />
        <ExperienceSection />
        <HireMeSection />
        <ContributionSection />
      </main>

      {/* Footer */}
      <Footer />
      </div>
    </SmoothScrollProvider>
  );
}
