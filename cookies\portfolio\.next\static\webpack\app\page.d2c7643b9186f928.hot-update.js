"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ContributionSection.tsx":
/*!*********************************************************!*\
  !*** ./src/components/sections/ContributionSection.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContributionSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _components_animations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/animations */ \"(app-pages-browser)/./src/components/animations/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ContributionSection() {\n    const username = 'Arkit-k';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"px-6 lg:px-12 mt-24 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-transparent to-blue-500/5 rounded-3xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations__WEBPACK_IMPORTED_MODULE_2__.SlideUp, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-sm rounded-3xl p-8 lg:p-12 border border-gray-700/50 shadow-2xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations__WEBPACK_IMPORTED_MODULE_2__.FadeIn, {\n                                delay: 0.2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-10 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-emerald-500/20 rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Github, {\n                                                                className: \"w-8 h-8 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                lineNumber: 25,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                            lineNumber: 24,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\",\n                                                            children: \"GitHub Contributions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                            lineNumber: 27,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                    lineNumber: 23,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-lg max-w-2xl leading-relaxed\",\n                                                    children: \"A visual representation of my coding journey and daily contributions to open source projects\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                    lineNumber: 31,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-6 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-emerald-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                    lineNumber: 38,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Active Contributor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                    lineNumber: 39,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                            lineNumber: 37,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-blue-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                    lineNumber: 42,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Daily Commits\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                    lineNumber: 43,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                            lineNumber: 41,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"https://github.com/\".concat(username),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"group flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-emerald-500/25 hover:scale-105\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 group-hover:rotate-12 transition-transform duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                            lineNumber: 55,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"View Profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                            lineNumber: 56,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 text-center\",\n                                                    children: [\n                                                        \"@\",\n                                                        username\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations__WEBPACK_IMPORTED_MODULE_2__.SlideUp, {\n                                delay: 0.4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -inset-1 bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-white flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-emerald-400 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                    lineNumber: 71,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Contribution Activity\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Less\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                    lineNumber: 75,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 bg-gray-700 rounded-sm\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                            lineNumber: 77,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 bg-emerald-800 rounded-sm\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                            lineNumber: 78,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 bg-emerald-600 rounded-sm\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                            lineNumber: 79,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 bg-emerald-400 rounded-sm\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                            lineNumber: 80,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 bg-emerald-300 rounded-sm\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                            lineNumber: 81,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                    lineNumber: 76,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"More\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                                    lineNumber: 83,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative overflow-hidden rounded-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            src: \"https://ghchart.rshah.org/\".concat(username),\n                                                            alt: \"\".concat(username, \"'s GitHub Contribution Graph\"),\n                                                            width: 1200,\n                                                            height: 220,\n                                                            className: \"w-full rounded-xl hover:scale-105 transition-transform duration-500\",\n                                                            unoptimized: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-gray-900/20 via-transparent to-transparent rounded-xl pointer-events-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 pt-4 border-t border-gray-700/50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 text-center\",\n                                                        children: \"Contributions in the last year • Updated daily\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\ContributionSection.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = ContributionSection;\nvar _c;\n$RefreshReg$(_c, \"ContributionSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ContributionSection.tsx\n"));

/***/ })

});